# 📊 Export CSV Feature Added - Mini Chat Foxy

## ✅ Đã thêm nút Export lịch sử chat ra CSV

### 🎯 **Tính năng:**
- ✅ **Nút Export CSV** trong sidebar Controls
- ✅ **Format CSV** theo yêu cầu: STT, Question, Model 1, Model 2, Model 3, Model 4, Model 5
- ✅ **Clean text** - loại bỏ markdown và HTML formatting
- ✅ **UTF-8 encoding** với BOM để hỗ trợ tiếng Việt
- ✅ **Auto filename** với ngày tháng

### 📋 **CSV Format:**

```csv
STT,Question,GPT-4,Claude-3,<PERSON> Pro,LLaMA-2,PaLM-2
1,"Hello! Can you introduce yourself?","I'm GPT-4, an AI assistant...","I'm <PERSON>, created by Anthropic...","","",""
2,"What is machine learning?","Machine learning is a subset...","ML is a field of AI that...","Machine learning involves...","","" 
```

### 🔧 **Thay đổi đã thực hiện:**

#### 1. **HTML Template (templates/index.html)**

```html
<button class="btn btn-outline-success btn-sm" onclick="exportChatHistory()" id="export-btn">
    <i class="fas fa-download me-1"></i> Export CSV
</button>
```

#### 2. **CSS Styling (static/css/style.css)**

```css
/* Export Button */
#export-btn {
    background: linear-gradient(45deg, #28a745, #20c997);
    border: none;
    transition: all 0.3s ease;
}

#export-btn:hover {
    background: linear-gradient(45deg, #20c997, #17a2b8);
    transform: translateY(-1px);
    box-shadow: 0 4px 15px rgba(40, 167, 69, 0.3);
}
```

#### 3. **JavaScript Functions (static/js/app.js)**

##### **Main Export Function:**
```javascript
function exportChatHistory() {
    if (!chatHistory || chatHistory.length === 0) {
        alert('No chat history to export!');
        return;
    }
    
    // Get all unique models from chat history
    const allModels = new Set();
    chatHistory.forEach(entry => {
        if (entry.responses) {
            entry.responses.forEach(response => {
                allModels.add(response.model || response.model_id);
            });
        }
    });
    
    const modelsList = Array.from(allModels).sort();
    
    // Create CSV header: STT,Question,Model1,Model2,Model3,Model4,Model5
    let csvContent = 'STT,Question';
    for (let i = 0; i < 5; i++) {
        const modelName = modelsList[i] || `Model ${i + 1}`;
        csvContent += `,${escapeCSV(modelName)}`;
    }
    csvContent += '\n';
    
    // Add data rows
    chatHistory.forEach((entry, index) => {
        const stt = index + 1;
        const question = entry.user_message || '';
        
        // Create responses map for easy lookup
        const responsesMap = {};
        if (entry.responses) {
            entry.responses.forEach(response => {
                const modelName = response.model || response.model_id;
                const rawText = response.raw_response || response.response || '';
                const cleanText = cleanTextForCSV(rawText);
                responsesMap[modelName] = cleanText;
            });
        }
        
        // Build CSV row
        let row = `${stt},${escapeCSV(question)}`;
        for (let i = 0; i < 5; i++) {
            const modelName = modelsList[i];
            const response = modelName ? (responsesMap[modelName] || '') : '';
            row += `,${escapeCSV(response)}`;
        }
        row += '\n';
        
        csvContent += row;
    });
    
    // Download file
    downloadCSV(csvContent, `chat_history_${new Date().toISOString().split('T')[0]}.csv`);
}
```

##### **Text Cleaning Function:**
```javascript
function cleanTextForCSV(text) {
    if (!text) return '';
    
    // Remove HTML tags
    let cleaned = text.replace(/<[^>]*>/g, '');
    
    // Remove markdown formatting
    cleaned = cleaned
        .replace(/\*\*(.*?)\*\*/g, '$1') // Bold **text**
        .replace(/\*(.*?)\*/g, '$1')     // Italic *text*
        .replace(/`(.*?)`/g, '$1')       // Code `text`
        .replace(/#{1,6}\s/g, '')        // Headers # ## ###
        .replace(/>\s/g, '')             // Quotes > text
        .replace(/\n\s*\n/g, '\n')       // Multiple newlines
        .replace(/\n/g, ' ')             // Single newlines to space
        .trim();
    
    return cleaned;
}
```

##### **CSV Escaping Function:**
```javascript
function escapeCSV(text) {
    if (!text) return '';
    
    // Convert to string and escape quotes
    let escaped = String(text).replace(/"/g, '""');
    
    // Wrap in quotes if contains comma, quote, or newline
    if (escaped.includes(',') || escaped.includes('"') || escaped.includes('\n')) {
        escaped = `"${escaped}"`;
    }
    
    return escaped;
}
```

##### **Download Function:**
```javascript
function downloadCSV(csvContent, filename) {
    // Add BOM for UTF-8 encoding (support Vietnamese)
    const BOM = '\uFEFF';
    const csvData = BOM + csvContent;
    
    // Create blob and download
    const blob = new Blob([csvData], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    
    if (link.download !== undefined) {
        const url = URL.createObjectURL(blob);
        link.setAttribute('href', url);
        link.setAttribute('download', filename);
        link.style.visibility = 'hidden';
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        URL.revokeObjectURL(url);
        
        showSuccessMessage(`Chat history exported to ${filename}`);
    }
}
```

### 🎨 **UI Design:**

#### **Sidebar Controls:**
```
┌─────────────────────────────┐
│ Controls:                   │
│ [⬇️ Collapse All]           │
│ [⬆️ Expand All]             │
│ [🔄 Reset Session]          │
│ [📊 Export CSV]             │ ← New button
└─────────────────────────────┘
```

### 📊 **CSV Output Example:**

```csv
STT,Question,qwen3-235b-a22b-07-25,google/gemini-2.5-flash-lite,deepseek-r1-0528,x-ai/grok-3-mini-beta,deepseek-chat-v3-0324
1,"Hello! Can you introduce yourself?","I'm an AI assistant created by Alibaba Cloud...","Hello! I'm Gemini, a large language model...","Hi there! I'm DeepSeek, an AI assistant...","Hey! I'm Grok, an AI with a sense of humor...","Hello! I'm DeepSeek Chat, here to help..."
2,"What is machine learning?","Machine learning is a subset of artificial intelligence...","ML is a method of data analysis that automates...","","",""
```

### 🔍 **Cách hoạt động:**

1. **Collect Models**: Lấy tất cả models unique từ chat history
2. **Sort Models**: Sắp xếp theo alphabet
3. **Create Header**: STT, Question, Model1-5 (nếu không đủ 5 models thì dùng "Model X")
4. **Process Entries**: Mỗi chat entry thành 1 row
5. **Clean Text**: Loại bỏ markdown/HTML formatting
6. **Escape CSV**: Xử lý quotes, commas, newlines
7. **Download**: Tạo file với UTF-8 BOM encoding

### 🎯 **Lợi ích:**

- ✅ **Structured Data**: Format CSV chuẩn cho Excel/Google Sheets
- ✅ **Clean Text**: Loại bỏ formatting, chỉ giữ nội dung
- ✅ **UTF-8 Support**: Hỗ trợ tiếng Việt và ký tự đặc biệt
- ✅ **Auto Filename**: Tên file với ngày tháng tự động
- ✅ **5 Columns**: Đúng format yêu cầu (STT + Question + 5 Models)
- ✅ **Empty Handling**: Cells trống nếu model không có response

### 🚀 **Sử dụng:**

1. **Chat với models** để tạo lịch sử
2. **Click "Export CSV"** trong sidebar
3. **File sẽ tự động download** với tên `chat_history_YYYY-MM-DD.csv`
4. **Mở trong Excel/Google Sheets** để xem và phân tích

## 🎉 **Ready to use!**

Tính năng export CSV đã hoàn thành và sẵn sàng sử dụng tại http://localhost:8081
