# 🐳 Mini Chat Foxy - Docker Setup

Simple Docker setup for Mini Chat Foxy application.

## 📋 Prerequisites

- Docker Engine 20.10+
- Docker Compose 2.0+

## 🚀 Quick Start

### 1. Setup Environment
```bash
# Copy environment file
cp .env.docker .env

# Edit .env with your API endpoint and configuration
nano .env
```

### 2. Run Application
```bash
# Build and start the application
docker-compose up -d --build

# View logs
docker-compose logs -f

# Stop application
docker-compose down
```

## 📁 Docker Files Structure

```
mini-chat-foxy/
├── Dockerfile              # Container configuration
├── docker-compose.yml      # Docker Compose file
├── .dockerignore           # Docker ignore rules
├── .env.docker            # Environment template
└── DOCKER_README.md       # This file
```

## 🛠️ Available Commands

```bash
# Start application (detached mode with build)
docker-compose up -d --build

# View logs
docker-compose logs -f

# Stop application
docker-compose down

# Access container shell
docker-compose exec mini-chat-foxy /bin/bash

# Restart application
docker-compose restart
```

## 🔧 Configuration

### Environment Variables

Key environment variables in `.env`:

```env
# API Configuration
API_ENDPOINT=http://localhost:8765/completion-test
API_TIMEOUT=300
USER_ID=your_user_id

# Application
SECRET_KEY=your-secret-key
DEBUG=False
PORT=8080

# Session Management
SESSION_TIMEOUT_HOURS=24
CLEANUP_INTERVAL_HOURS=1
```

### Service Configuration

The Docker setup includes:

1. **mini-chat-foxy**: Main Flask application

## 🌐 Accessing the Application

- **Application**: http://localhost:8080

## 📊 Monitoring and Debugging

### View Logs
```bash
docker-compose logs -f
```

### Access Container Shell
```bash
docker-compose exec mini-chat-foxy /bin/bash
```

### Health Check
```bash
curl http://localhost:8080/api/models
```

## 🧹 Maintenance

### Clean Up
```bash
# Remove containers and networks
docker-compose down

# Remove everything including images
docker-compose down --rmi all
```

## 🐛 Troubleshooting

### Common Issues

1. **Port already in use**
   ```bash
   # Check what's using the port
   lsof -i :8080

   # Change port in .env
   PORT=8081
   ```

2. **Container won't start**
   ```bash
   # Check logs
   docker-compose logs -f

   # Rebuild containers
   docker-compose up --build
   ```

3. **API connection issues**
   ```bash
   # Check API endpoint in .env
   # Test API endpoint manually
   curl -X POST http://localhost:8765/completion-test
   ```

This Docker setup provides a simple containerized environment for Mini Chat Foxy.
