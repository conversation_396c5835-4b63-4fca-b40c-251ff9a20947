from flask import Flask, render_template, request, jsonify, session
from flask_cors import CORS
import random
import time
import markdown
import uuid
from datetime import datetime, timedelta
import threading
import requests
import os
from dotenv import load_dotenv
from concurrent.futures import ThreadPoolExecutor, as_completed

# Load environment variables
load_dotenv()

app = Flask(__name__)
app.secret_key = os.getenv('SECRET_KEY', 'your-secret-key-change-in-production')
CORS(app)

# Environment variables
API_ENDPOINT = os.getenv('API_ENDPOINT', 'https://your-api-endpoint.com/chat')
API_CONTINUE_ENDPOINT = os.getenv('API_CONTINUE_ENDPOINT', 'https://your-api-continue-endpoint.com/chat')
API_TIMEOUT = int(os.getenv('API_TIMEOUT', '30'))
USER_ID = os.getenv('USER_ID', 'default_user_123')
DEBUG = os.getenv('DEBUG', 'True').lower() == 'true'
HOST = os.getenv('HOST', '0.0.0.0')
PORT = int(os.getenv('PORT', '5000'))

# Mock data cho models và characters
MODELS = [
    {"id": "qwen3-235b-a22b-07-25", "name": "qwen3-235b-a22b-07-25"},
    {"id": "gemini-2.5-flash-lite", "name": "google/gemini-2.5-flash-lite"},
    {"id": "deepseek-r1-0528", "name": "deepseek-r1-0528"},
    {"id": "grok-3-mini-beta", "name": "x-ai/grok-3-mini-beta"},
    {"id": "deepseek-chat-v3-0324", "name": "deepseek-chat-v3-0324"}
]

CHARACTERS = [
    {"id": "001_hinata", "name": "Hinata", "personality": "Hinata is Submissive"},
    {"id": "001_taesung", "name": "TaeSung", "personality": "Taesung is Dominant"}
]

PROMPT_VERSIONS = [
    {"id": "v1", "name": "Version 1.0", "description": "Standard prompt"},
    {"id": "v2", "name": "Version 2.0", "description": "Enhanced with context"},
    {"id": "v3", "name": "Version 3.0", "description": "Creative and detailed"},
    {"id": "v4", "name": "Version 4.0", "description": "Technical and precise"},
    {"id": "v5", "name": "Version 5.0", "description": "Conversational style"}
]

# Session storage - In production, use Redis or database
sessions_storage = {}
session_lock = threading.Lock()

# Session configuration
SESSION_TIMEOUT = timedelta(hours=int(os.getenv('SESSION_TIMEOUT_HOURS', '24')))
CLEANUP_INTERVAL = timedelta(hours=int(os.getenv('CLEANUP_INTERVAL_HOURS', '1')))

# Session Management Functions
def generate_session_id():
    """Generate a unique session ID using UUID4 format"""
    return str(uuid.uuid4())

def create_session(session_id=None):
    """Create a new session with the given ID or generate a new one"""
    if session_id is None:
        session_id = generate_session_id()

    now = datetime.now()
    session_data = {
        'session_id': session_id,
        'created_at': now,
        'last_activity': now,
        'chat_history': [],
        'user_preferences': {
            'selected_models': ['gpt-4', 'claude-3', 'gemini-pro', 'llama-2', 'palm-2'],
            'character': 'assistant',
            'prompt_version': 'v1'
        }
    }

    with session_lock:
        sessions_storage[session_id] = session_data

    return session_id, session_data

def get_session(session_id):
    """Get session data by session ID"""
    with session_lock:
        return sessions_storage.get(session_id)

def update_session_activity(session_id):
    """Update the last activity time for a session"""
    with session_lock:
        if session_id in sessions_storage:
            sessions_storage[session_id]['last_activity'] = datetime.now()
            return True
    return False

def add_to_chat_history(session_id, message_data):
    """Add a message and responses to session chat history"""
    with session_lock:
        if session_id in sessions_storage:
            sessions_storage[session_id]['chat_history'].append(message_data)
            return True
    return False

def cleanup_expired_sessions():
    """Remove expired sessions"""
    now = datetime.now()
    expired_sessions = []

    with session_lock:
        for session_id, session_data in sessions_storage.items():
            if now - session_data['last_activity'] > SESSION_TIMEOUT:
                expired_sessions.append(session_id)

        for session_id in expired_sessions:
            del sessions_storage[session_id]

    return len(expired_sessions)

class APIRequest:
    """Class to handle API request structure and validation"""

    def __init__(self, query: str, user_id: str, character_id: str,
                 session_id: str, prompt_id: str, model_name: str, number_of_sentence: int = 8):
        self.query = self._validate_string(query, "query")
        self.user_id = self._validate_string(user_id, "user_id")
        self.character_id = self._validate_string(character_id, "character_id")
        self.session_id = self._validate_string(session_id, "session_id")
        self.prompt_id = self._validate_string(prompt_id, "prompt_id")
        self.scenario_mode = True  # Always True as per requirement
        self.model_name = self._validate_string(model_name, "model_name")
        self.number_of_sentence = self._validate_int(number_of_sentence, "number_of_sentence")

    def _validate_string(self, value: str, field_name: str) -> str:
        """Validate that field is a non-empty string"""
        if not isinstance(value, str) or not value.strip():
            raise ValueError(f"{field_name} must be a non-empty string")
        return value.strip()

    def _validate_int(self, value: int, field_name: str) -> int:
        """Validate that field is a positive integer"""
        if not isinstance(value, int) or value <= 0:
            raise ValueError(f"{field_name} must be a positive integer")
        return value

    def to_dict(self) -> dict:
        """Convert to dictionary for API request"""
        return {
            "query": self.query,
            "user_id": self.user_id,
            "character_id": self.character_id,
            "session_id": self.session_id,
            "prompt_id": self.prompt_id,
            "scenario_mode": self.scenario_mode,
            "model_name": self.model_name,
            "number_of_sentence": self.number_of_sentence
        }

def send_api_request(api_request: APIRequest, endpoint_url: str = None) -> dict:
    """
    Send request to external API endpoint

    Args:
        api_request (APIRequest): Structured API request object

    Returns:
        dict: Response with success status and data/error
    """
    try:
        payload = api_request.to_dict()
        payload["session_id"] = payload["session_id"] + "-" + payload["model_name"]
        print(f"Sending API request for model: {api_request.model_name}")
        print(f"Payload: {payload}")

        # Use provided endpoint or default to API_ENDPOINT
        api_url = endpoint_url if endpoint_url else API_ENDPOINT

        response = requests.post(
            api_url,
            json=payload,
            timeout=API_TIMEOUT,
            headers={
                'Content-Type': 'application/json',
                'Accept': 'application/json'
            }
        )

        print(f"Response status for {api_request.model_name}: {response.status_code}")

        if response.status_code == 200:
            try:
                response_data = response.json()
                return {
                    'success': True,
                    'data': response_data,
                    'model_name': api_request.model_name
                }
            except ValueError as e:
                return {
                    'success': False,
                    'error': f'Invalid JSON response from API: {str(e)}',
                    'details': response.text[:500] + '...' if len(response.text) > 500 else response.text,
                    'model_name': api_request.model_name
                }
        else:
            return {
                'success': False,
                'error': f'API request failed with status {response.status_code}',
                'details': response.text[:500] + '...' if len(response.text) > 500 else response.text,
                'model_name': api_request.model_name
            }

    except requests.exceptions.Timeout:
        return {
            'success': False,
            'error': f'Request timeout after {API_TIMEOUT} seconds',
            'model_name': api_request.model_name
        }
    except requests.exceptions.ConnectionError:
        return {
            'success': False,
            'error': f'Unable to connect to API endpoint: {API_ENDPOINT}',
            'model_name': api_request.model_name
        }
    except requests.exceptions.RequestException as e:
        return {
            'success': False,
            'error': f'Request failed: {str(e)}',
            'model_name': api_request.model_name
        }
    except Exception as e:
        return {
            'success': False,
            'error': f'Unexpected error occurred: {str(e)}',
            'model_name': api_request.model_name
        }

def get_session_info(session_id):
    """Get basic session information"""
    session_data = get_session(session_id)
    if session_data:
        return {
            'session_id': session_id,
            'created_at': session_data['created_at'].isoformat(),
            'last_activity': session_data['last_activity'].isoformat(),
            'chat_count': len(session_data['chat_history']),
            'is_active': True
        }
    return None

@app.route('/')
def index():
    # Generate or get session ID
    session_id = session.get('session_id')
    if not session_id or not get_session(session_id):
        session_id, session_data = create_session()
        session['session_id'] = session_id
    else:
        update_session_activity(session_id)
        session_data = get_session(session_id)

    return render_template('index.html',
                         models=MODELS,
                         characters=CHARACTERS,
                         prompt_versions=PROMPT_VERSIONS,
                         session_id=session_id,
                         chat_history=session_data['chat_history'])

def process_model_request(model_id: str, message: str, session_id: str,
                         character: str, prompt_version: str, chat_type: str = 'chat',
                         verbosity_level: int = 2, number_of_sentence: int = 8) -> dict:
    """
    Process API request for a single model

    Args:
        model_id (str): Model identifier
        message (str): User message
        session_id (str): Session ID
        character (str): Character ID
        prompt_version (str): Prompt version ID

    Returns:
        dict: Processed response for the model
    """
    model_name = next((m['name'] for m in MODELS if m['id'] == model_id), model_id)

    try:
        # Create structured API request
        api_request = APIRequest(
            query=message,
            user_id=USER_ID,
            character_id=character,
            session_id=session_id,
            prompt_id=prompt_version,
            model_name=model_id,
            number_of_sentence=number_of_sentence
        )

        # Determine endpoint based on chat type
        endpoint_url = API_CONTINUE_ENDPOINT if chat_type == 'continue' else API_ENDPOINT

        # Send request to API endpoint
        api_result = send_api_request(api_request, endpoint_url)

        if api_result['success']:
            # Process successful API response
            api_data = api_result['data']
            response_text = ""

            # Extract response text from API with specific format: data.answer.content
            if isinstance(api_data, dict):
                # Try to extract from the specific API format: data.answer.content
                if 'data' in api_data and isinstance(api_data['data'], dict):
                    data_section = api_data['data']
                    if 'answer' in data_section and isinstance(data_section['answer'], dict):
                        answer_section = data_section['answer']
                        if 'content' in answer_section:
                            content = answer_section['content']
                            # Content is a list with one string
                            if isinstance(content, list) and len(content) > 0:
                                response_text = content[0]
                            elif isinstance(content, str):
                                response_text = content

                # Fallback to other common response fields if specific format not found
                if not response_text:
                    response_text = api_data.get('response',
                                               api_data.get('message',
                                                          api_data.get('content',
                                                                     api_data.get('text', str(api_data)))))
            else:
                response_text = str(api_data)

            # Ensure we have valid response text
            if not response_text or response_text.strip() == '':
                response_text = "No response content received from API"

            # Convert markdown to HTML
            html_response = markdown.markdown(response_text, extensions=['codehilite', 'fenced_code'])

            return {
                'model': model_name,
                'model_id': model_id,
                'response': html_response,
                'raw_response': response_text,
                'api_success': True,
                'processing_time': time.time(),
                'api_data': api_data  # Include full API data for debugging
            }
        else:
            # Handle API error - show clear error message
            error_message = f"**API Error - {model_name}**\n\n❌ {api_result['error']}"

            # Add details if available
            if 'details' in api_result and api_result['details']:
                error_message += f"\n\n**Details:** {api_result['details']}"

            html_response = markdown.markdown(error_message, extensions=['codehilite', 'fenced_code'])

            return {
                'model': model_name,
                'model_id': model_id,
                'response': html_response,
                'raw_response': error_message,
                'api_success': False,
                'api_error': api_result['error'],
                'processing_time': time.time()
            }

    except ValueError as e:
        # Handle validation errors
        error_message = f"**Validation Error - {model_name}**\n\n❌ {str(e)}"
        html_response = markdown.markdown(error_message, extensions=['codehilite', 'fenced_code'])

        return {
            'model': model_name,
            'model_id': model_id,
            'response': html_response,
            'raw_response': error_message,
            'api_success': False,
            'api_error': f'Validation error: {str(e)}',
            'processing_time': time.time()
        }

@app.route('/api/chat', methods=['POST'])
def chat():
    """
    Handle chat requests - sends parallel requests to selected models
    """
    data = request.json

    # Validate required fields
    if not data:
        return jsonify({'success': False, 'error': 'No JSON data provided'}), 400

    message = data.get('message', '').strip()
    selected_models = data.get('models', [])
    character = data.get('character', 'assistant')
    prompt_version = data.get('prompt_version', 'v1')
    session_id = data.get('session_id') or session.get('session_id')
    chat_type = data.get('chat_type', 'chat')
    verbosity_level = data.get('verbosity_level', 2)
    number_of_sentence = data.get('number_of_sentence', 8)

    # Validate inputs
    if not message:
        return jsonify({'success': False, 'error': 'Message cannot be empty'}), 400

    if not selected_models:
        return jsonify({'success': False, 'error': 'No models selected'}), 400

    if not session_id:
        return jsonify({'success': False, 'error': 'No session ID provided'}), 400

    # Validate session
    session_data = get_session(session_id)
    if not session_data:
        return jsonify({'success': False, 'error': 'Invalid session ID'}), 400

    # Validate API endpoint is configured
    if not API_ENDPOINT or API_ENDPOINT == 'https://your-api-endpoint.com/chat':
        return jsonify({
            'success': False,
            'error': 'API endpoint not configured. Please set API_ENDPOINT in .env file'
        }), 500

    # Update session activity
    update_session_activity(session_id)

    print(f"Processing chat request:")
    print(f"  Message: {message}")
    print(f"  Models: {selected_models}")
    print(f"  Character: {character}")
    print(f"  Prompt Version: {prompt_version}")
    print(f"  Session ID: {session_id}")
    print(f"  Chat Type: {chat_type}")
    print(f"  Verbosity Level: {verbosity_level}")
    print(f"  Number of Sentences: {number_of_sentence}")

    # Process requests for all selected models in parallel
    responses = []
    start_time = time.time()

    with ThreadPoolExecutor(max_workers=len(selected_models)) as executor:
        # Submit all requests
        future_to_model = {
            executor.submit(process_model_request, model_id, message, session_id, character, prompt_version, chat_type, verbosity_level, number_of_sentence): model_id
            for model_id in selected_models
        }

        # Collect results as they complete
        for future in as_completed(future_to_model):
            model_id = future_to_model[future]
            try:
                result = future.result()
                responses.append(result)
                print(f"Completed request for model: {model_id}")
            except Exception as exc:
                print(f"Model {model_id} generated an exception: {exc}")
                # Add error response
                model_name = next((m['name'] for m in MODELS if m['id'] == model_id), model_id)
                error_message = f"**Processing Error - {model_name}**\n\n❌ Failed to process request: {str(exc)}"
                html_response = markdown.markdown(error_message, extensions=['codehilite', 'fenced_code'])

                responses.append({
                    'model': model_name,
                    'model_id': model_id,
                    'response': html_response,
                    'raw_response': error_message,
                    'api_success': False,
                    'api_error': f'Processing error: {str(exc)}',
                    'processing_time': time.time()
                })

    total_time = time.time() - start_time
    print(f"All requests completed in {total_time:.2f} seconds")

    # Sort responses by model order to maintain consistency
    model_order = {model_id: i for i, model_id in enumerate(selected_models)}
    responses.sort(key=lambda x: model_order.get(x['model_id'], 999))

    # Count successful/failed requests
    successful_requests = sum(1 for r in responses if r['api_success'])
    failed_requests = len(responses) - successful_requests

    # Store in chat history
    chat_entry = {
        'timestamp': datetime.now().isoformat(),
        'user_message': message,
        'character': character,
        'prompt_version': prompt_version,
        'selected_models': selected_models,
        'responses': responses,
        'processing_time': total_time,
        'successful_requests': successful_requests,
        'failed_requests': failed_requests,
        'chat_type': chat_type,
        'verbosity_level': verbosity_level,
        'number_of_sentence': number_of_sentence
    }
    add_to_chat_history(session_id, chat_entry)

    # Get character info
    character_info = next((c for c in CHARACTERS if c['id'] == character), CHARACTERS[0])

    return jsonify({
        'success': True,
        'responses': responses,
        'character': character_info['name'],
        'session_id': session_id,
        'processing_time': total_time,
        'successful_requests': successful_requests,
        'failed_requests': failed_requests,
        'total_requests': len(responses),
        'chat_type': chat_type,
        'verbosity_level': verbosity_level,
        'number_of_sentence': number_of_sentence
    })

@app.route('/api/chat/continue', methods=['POST'])
def chat_continue():
    """
    Handle continue chat requests - uses different API endpoint
    """
    # Reuse the same logic as regular chat but force chat_type to 'continue'
    data = request.json
    if data:
        data['chat_type'] = 'continue'

    # Call the regular chat function which now handles chat_type
    return chat()

@app.route('/api/models')
def get_models():
    return jsonify(MODELS)

@app.route('/api/characters')
def get_characters():
    return jsonify(CHARACTERS)

@app.route('/api/prompt-versions')
def get_prompt_versions():
    return jsonify(PROMPT_VERSIONS)

# Session Management Routes
@app.route('/api/session/new', methods=['POST'])
def create_new_session():
    """Create a new session"""
    session_id, session_data = create_session()
    session['session_id'] = session_id

    return jsonify({
        'success': True,
        'session_id': session_id,
        'created_at': session_data['created_at'].isoformat()
    })

@app.route('/api/session/info')
def session_info():
    """Get current session information"""
    session_id = request.args.get('session_id') or session.get('session_id')

    if not session_id:
        return jsonify({'success': False, 'error': 'No session ID'}), 400

    info = get_session_info(session_id)
    if info:
        return jsonify({'success': True, 'session': info})
    else:
        return jsonify({'success': False, 'error': 'Session not found'}), 404

@app.route('/api/session/history')
def session_history():
    """Get chat history for a session"""
    session_id = request.args.get('session_id') or session.get('session_id')

    if not session_id:
        return jsonify({'success': False, 'error': 'No session ID'}), 400

    session_data = get_session(session_id)
    if session_data:
        return jsonify({
            'success': True,
            'session_id': session_id,
            'chat_history': session_data['chat_history']
        })
    else:
        return jsonify({'success': False, 'error': 'Session not found'}), 404

@app.route('/api/session/cleanup', methods=['POST'])
def cleanup_sessions():
    """Manually trigger session cleanup"""
    cleaned_count = cleanup_expired_sessions()
    return jsonify({
        'success': True,
        'cleaned_sessions': cleaned_count,
        'active_sessions': len(sessions_storage)
    })

@app.route('/api/test-endpoint', methods=['POST'])
def test_api_endpoint():
    """Test the external API endpoint"""
    data = request.json or {}
    test_query = data.get('query', 'Hello, this is a test message')
    test_model = data.get('model', 'gpt-4')
    test_character = data.get('character', 'assistant')
    test_prompt = data.get('prompt_version', 'v1')
    test_session_id = data.get('session_id', 'test-session-123')

    try:
        # Create structured API request
        api_request = APIRequest(
            query=test_query,
            user_id=USER_ID,
            character_id=test_character,
            session_id=test_session_id,
            prompt_id=test_prompt,
            model_name=test_model,
            number_of_sentence=8  # Default test value
        )

        result = send_api_request(api_request)

        return jsonify({
            'test_result': result,
            'endpoint': API_ENDPOINT,
            'user_id': USER_ID,
            'payload_sent': api_request.to_dict(),
            'timestamp': datetime.now().isoformat()
        })

    except ValueError as e:
        return jsonify({
            'test_result': {
                'success': False,
                'error': f'Validation error: {str(e)}'
            },
            'endpoint': API_ENDPOINT,
            'user_id': USER_ID,
            'payload_attempted': {
                "query": test_query,
                "user_id": USER_ID,
                "character_id": test_character,
                "session_id": test_session_id,
                "prompt_id": test_prompt,
                "scenario_mode": True,
                "model_name": test_model
            },
            'timestamp': datetime.now().isoformat()
        }), 400

if __name__ == '__main__':
    app.run(debug=DEBUG, host=HOST, port=PORT)
