import numpy as np
import pandas as pd # Not strictly needed for core logic now, but often useful
from datetime import datetime, timedelta
from typing import Dict, List, Tuple, Optional, Any
from dataclasses import dataclass, field
import json
from collections import defaultdict
import random
import re
from pathlib import Path
import logging # Import the logging module

# --- Logging Setup ---
def setup_logging(level=logging.DEBUG):
    """Configures basic logging."""
    logging.basicConfig(
        level=level,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        datefmt='%Y-%m-%d %H:%M:%S'
    )

# --- Constants for Categories ---
CAT_ENGAGEMENT = "engagement"
CAT_SOCIAL = "social"
CAT_MONETIZATION = "monetization"
CAT_RETENTION = "retention"
CAT_ACTIVITY = "activity"
CAT_ROMANTIC = "romantic"
CAT_PREMIUM = "premium"
CAT_NSFW = "nsfw"
CAT_COMFORT = "comfort"
CAT_INTERACTIVE = "interactive"
CAT_GRATITUDE = "gratitude"
CAT_MILESTONE = "milestone"

# A list of known categories for documentation or sample data generation.
KNOWN_NOTIFICATION_CATEGORIES = [
    CAT_ENGAGEMENT, CAT_SOCIAL, CAT_MONETIZATION, CAT_RETENTION,
    CAT_ACTIVITY, CAT_ROMANTIC, CAT_PREMIUM, CAT_NSFW, CAT_COMFORT,
    CAT_INTERACTIVE, CAT_GRATITUDE, CAT_MILESTONE
]

# --- Dataclasses ---
@dataclass
class NotificationTemplate:
    notification_id: str; title: str; content: str; notification_type: str
    category: str; nsfw_level: int; engagement_score: float
    user_context_requirements: Dict[str, Any]; placeholder_variables: List[str]

@dataclass
class UserNotificationPreferences:
    user_id: str; preferred_hours: List[int]; preferred_days: List[int]
    max_notifications_per_day: int; time_zone_offset: int
    do_not_disturb_start: int; do_not_disturb_end: int
    notification_frequency: str; nsfw_tolerance: int
    preferred_categories: List[str] = field(default_factory=list)
    blocked_categories: List[str] = field(default_factory=list)

@dataclass
class UserContext:
    user_id: str; date: datetime; subscription_status: str; interests: str
    gender: str; age: int; gifts_given: int; diamonds_spent: float
    num_chat_characters: int; messages_per_day: float; notification_click_rate: float
    recent_chat_characters: List[str]; top_chat_characters: List[str]
    online_time_periods: List[Tuple[datetime, datetime]]; last_online_time: datetime
    day_of_week: int; hour_of_day: int; cumulative_clicks: int; cumulative_notifications: int
    recent_activity_level: float; preferred_character_types: List[str]; current_mood: str
    notification_preferences: UserNotificationPreferences

# --- Classes with Logging ---

class NotificationContentManager:
    def __init__(self, notification_json_path: str):
        self.logger = logging.getLogger(self.__class__.__name__)
        self.logger.info(f"Khởi tạo ContentManager, đang tải từ '{notification_json_path}'")
        self.templates: Dict[str, NotificationTemplate] = {}
        self.category_mapping: Dict[str, List[str]] = defaultdict(list)
        self.all_loaded_categories: set[str] = set()
        try:
            self.load_templates(notification_json_path)
            self.setup_category_mappings()
            self.logger.info(f"ContentManager khởi tạo hoàn tất, đã tải {len(self.templates)} mẫu.")
        except Exception as e:
            self.logger.critical(f"LỖI NGHIÊM TRỌNG khi khởi tạo ContentManager: {e}", exc_info=True)
            raise

    def load_templates(self, json_path: str):
        self.logger.debug(f"Bắt đầu tải mẫu từ: {json_path}")
        try:
            with open(json_path, 'r', encoding='utf-8') as f:
                notifications = json.load(f)
            self.logger.info(f"Đã đọc thành công {len(notifications)} mục từ tệp JSON.")
        except FileNotFoundError:
            self.logger.error(f"Không tìm thấy tệp thông báo '{json_path}'.")
            raise
        except json.JSONDecodeError as e:
            self.logger.error(f"Tệp thông báo '{json_path}' không phải là JSON hợp lệ: {e}")
            raise
        
        for i, notification_data in enumerate(notifications):
            notif_id_from_json = notification_data.get('notification_id', notification_data.get('id'))
            template_id = notif_id_from_json if notif_id_from_json else f"notif_data_{i:04d}"
            try:
                template = self.create_template_from_json(notification_data, template_id)
                self.templates[template.notification_id] = template
                self.all_loaded_categories.add(template.category)
            except Exception as e:
                self.logger.error(f"Lỗi khi tạo mẫu từ dữ liệu (id: {template_id}, index: {i}): {notification_data}. Lỗi: {e}", exc_info=True)
        self.logger.debug(f"Hoàn thành tải mẫu, tổng số mẫu đã xử lý: {len(self.templates)}")

    def create_template_from_json(self, notification_data: Dict, template_id: str) -> NotificationTemplate:
        notification_type = notification_data.get('type', 'Chưa rõ')
        title = notification_data.get('title', '')
        content = notification_data.get('content', '')
        explicit_category = notification_data.get('category')
        if explicit_category and isinstance(explicit_category, str) and explicit_category.strip():
            category = explicit_category.lower().strip().replace(" ", "_")
        else:
            category = self.determine_category_from_fields(notification_type, title, content)
        nsfw_level = self.determine_nsfw_level(notification_type, title, content)
        placeholders = self.extract_placeholders(title + ' ' + content)
        engagement_score = self.calculate_engagement_score(notification_type, content)
        context_requirements = self.determine_context_requirements(notification_type, placeholders)
        return NotificationTemplate(
            notification_id=template_id, title=title, content=content, notification_type=notification_type,
            category=category, nsfw_level=nsfw_level, engagement_score=engagement_score,
            user_context_requirements=context_requirements, placeholder_variables=placeholders
        )

    def determine_category_from_fields(self, notification_type: str, title: str, content: str) -> str:
        type_lower = notification_type.lower(); title_lower = title.lower(); content_lower = content.lower()
        if any(kw in type_lower for kw in ['nsfw', 'explicit', 'sexual', 'kinky']): return CAT_NSFW
        if any(kw in content_lower for kw in ['cock', 'pussy', 'dick', 'cum', 'fuck', 'ass', 'tits', 'horny', 'wet', 'hard']): return CAT_NSFW
        if any(kw in type_lower for kw in ['comfort', 'cute', 'sweet', 'care', 'support']): return CAT_COMFORT
        if any(kw in type_lower for kw in ['match', 'like', 'message', 'view', 'chat', 'friend', 'connect']): return CAT_SOCIAL
        if any(kw in type_lower for kw in ['premium', 'offer', 'boost', 'upgrade', 'vip', 'discount', 'sale', 'coin', 'gem']): return CAT_MONETIZATION
        if any(kw in type_lower for kw in ['miss', 'inactive', 'reminder', 'comeback', 'return', 'away']): return CAT_RETENTION
        if any(kw in type_lower for kw in ['interactive', 'poll', 'dare', 'question', 'challenge', 'quiz', 'game']): return CAT_INTERACTIVE
        if any(kw in type_lower for kw in ['gratitude', 'thanks', 'appreciate', 'kudos']): return CAT_GRATITUDE
        if any(kw in type_lower for kw in ['milestone', 'achievement', 'streak', 'anniversary', 'record']): return CAT_MILESTONE
        if any(kw in type_lower for kw in ['activity', 'online', 'nearby', 'live', 'update']): return CAT_ACTIVITY
        if any(kw in type_lower for kw in ['new', 'alert', 'check out']): return CAT_ENGAGEMENT
        if type_lower in KNOWN_NOTIFICATION_CATEGORIES: return type_lower
        return CAT_ROMANTIC # Default fallback

    def determine_nsfw_level(self, nt: str, title: str, content: str) -> int:
        txt = (nt + ' ' + title + ' ' + content).lower()
        if any(kw in txt for kw in ['cock', 'pussy', 'dick', 'cum', 'fuck', 'ass', 'tits', 'horny', 'wet', 'hard', 'suck', 'lick', 'ride', 'pound', 'thrust', 'penetrate', 'orgasm', 'climax']): return 3
        if any(kw in txt for kw in ['sexy', 'hot', 'desire', 'passion', 'intimate', 'sensual', 'seductive', 'naughty', 'dirty', 'kiss', 'touch', 'caress', 'lust', 'arouse']): return 2
        if any(kw in txt for kw in ['attractive', 'cute', 'beautiful', 'handsome', 'flirt', 'charm', 'romantic', 'date', 'love', 'crush', 'sweetheart', 'darling']): return 1
        return 0

    def extract_placeholders(self, text: str) -> List[str]: return list(set(re.findall(r'\{([^}]+)\}', text)))
    def calculate_engagement_score(self, nt: str, content: str) -> float:
        bs = 0.3; type_l = nt.lower()
        if any(ht.lower() in type_l for ht in ['New Match', 'Message Received', 'Super Like Received', 'Gift Received']): bs = 0.7
        elif any(mt.lower() in type_l for mt in ['Like Received', 'Profile View', 'Interactive Poll', 'Challenge Issued']): bs = 0.5
        elif any(lt.lower() in type_l for lt in ['Special Offer', 'Premium Feature', 'Inactive Reminder', 'General Announcement']): bs = 0.2
        if len(content) > 150: bs *= 0.9
        if '?' in content: bs = min(1.0, bs * 1.15)
        bs = min(1.0, bs * (1 + len(re.findall(r'[😀-🙏]', content)) * 0.05))
        return min(max(bs, 0.1), 1.0)
    def determine_context_requirements(self, nt: str, pl: List[str]) -> Dict[str, Any]:
        req = {}
        if 'match_name' in pl: req['has_recent_matches'] = True
        if 'sender_name' in pl: req['has_recent_messages'] = True
        if 'character_name' in pl: req['has_chat_characters'] = True
        if 'user_body_part' in pl or 'body_part' in pl: req['nsfw_tolerance_min'] = 2
        if 'Premium' in nt or 'Offer' in nt: req['subscription_status'] = 'free'
        if 'Inactive' in nt: req['low_recent_activity'] = True
        if 'Match' in nt: req['has_recent_matches'] = True
        return req

    def setup_category_mappings(self):
        self.logger.debug("Đang thiết lập ánh xạ danh mục.")
        self.category_mapping.clear()
        for tid, t in self.templates.items(): self.category_mapping[t.category].append(tid)
        self.all_loaded_categories = set(self.category_mapping.keys())
        self.logger.debug(f"Hoàn thành thiết lập ánh xạ, số danh mục duy nhất: {len(self.all_loaded_categories)}")

    def get_all_categories(self) -> List[str]: return sorted(list(self.all_loaded_categories))

    def get_suitable_notifications(self, uc: UserContext) -> List[NotificationTemplate]:
        self.logger.debug(f"Đang lấy các thông báo phù hợp cho người dùng: {uc.user_id}")
        s = []; prefs = uc.notification_preferences
        for t in self.templates.values():
            if t.nsfw_level > prefs.nsfw_tolerance: continue
            if t.category in prefs.blocked_categories: continue
            if prefs.preferred_categories and t.category not in prefs.preferred_categories: continue
            if self.meets_context_requirements(t, uc): s.append(t)
        self.logger.debug(f"Tìm thấy {len(s)} thông báo phù hợp cho người dùng {uc.user_id}")
        return s
        
    def meets_context_requirements(self, t: NotificationTemplate, uc: UserContext) -> bool:
        req = t.user_context_requirements;
        if not req: return True
        if 'subscription_status' in req and uc.subscription_status != req['subscription_status']: return False
        if req.get('has_recent_matches', False) and not uc.recent_chat_characters: return False
        if req.get('has_recent_messages', False) and uc.messages_per_day < 1: return False
        if req.get('has_chat_characters', False) and uc.num_chat_characters == 0: return False
        if 'nsfw_tolerance_min' in req and uc.notification_preferences.nsfw_tolerance < req['nsfw_tolerance_min']: return False
        if req.get('low_recent_activity', False) and uc.recent_activity_level > 0.3: return False
        return True
        
    def personalize_notification(self, t: NotificationTemplate, uc: UserContext) -> Tuple[str, str]:
        title = t.title; content = t.content
        rps = {
            'user_name': uc.user_id.replace('_', ' ').title(),
            'character_name': random.choice(uc.recent_chat_characters) if uc.recent_chat_characters else 'Alex',
            'match_name': random.choice(uc.recent_chat_characters) if uc.recent_chat_characters else 'Ai đó',
            'sender_name': random.choice(uc.recent_chat_characters) if uc.recent_chat_characters else 'Ai đó',
            'city': 'thành phố của bạn', 'age': str(random.randint(max(18, uc.age - 5), uc.age + 5)),
            'distance': f"{random.randint(1, 10)} dặm", 'interests': random.choice(['âm nhạc', 'nghệ thuật', 'leo núi']),
            'day_of_week': uc.date.strftime('%A'), 'body_part': self.get_appropriate_body_part(uc),
            'user_body_part': self.get_appropriate_body_part(uc),
            'body_part_sexual': self.get_appropriate_body_part(uc, sexual=True),
            'body_part_adj': random.choice(['tuyệt vời', 'kinh ngạc']), 'time_ago': f"{random.randint(1, 23)} giờ trước",
            'number': str(random.randint(2, 10)), 'percentage': str(random.randint(10, 90)),
            'discount_percentage': str(random.choice([10, 20, 30, 50])),
            'message_snippet': 'Chào, mình thấy hồ sơ của bạn thú vị! Bạn khỏe không?',
            'event_name': 'Giao Lưu Hàng Tuần', 'gift_name': random.choice(['Bông Hồng', 'Trái Tim Kim Cương']),
        }
        if uc.preferred_character_types: rps['character_type'] = random.choice(uc.preferred_character_types)
        for p, rv_fn in rps.items():
            if f'{{{p}}}' in title or f'{{{p}}}' in content:
                v = rv_fn() if callable(rv_fn) else rv_fn
                title = title.replace(f'{{{p}}}', str(v)); content = content.replace(f'{{{p}}}', str(v))
        return title, content
        
    def get_appropriate_body_part(self, uc: UserContext, sexual: bool = False) -> str:
        nsfw_tol = uc.notification_preferences.nsfw_tolerance; gender = uc.gender.lower()
        if sexual:
            if nsfw_tol >= 3: return random.choice(['mông', 'ngực', 'cậu nhỏ', 'cô bé'])
            elif nsfw_tol >= 2: return random.choice(['đường cong', 'thân hình', 'đôi môi']) if gender == 'female' else random.choice(['thân hình', 'cơ bụng', 'ngực'])
        if nsfw_tol >= 1: return random.choice(['nụ cười', 'đôi mắt', 'phong cách', 'mái tóc'])
        return random.choice(['tính cách', 'rung cảm', 'năng lượng', 'khiếu hài hước'])

class EnhancedNotificationBandit:
    def __init__(self, content_manager: NotificationContentManager, alpha: float = 0.1):
        self.logger = logging.getLogger(self.__class__.__name__)
        self.logger.info(f"Khởi tạo Bandit cho content_manager có {len(content_manager.templates)} templates.")
        self.content_manager = content_manager
        self.alpha = alpha
        self.feature_extractor = EnhancedFeatureExtractor()

        self.category_performance = defaultdict(lambda: {'sent': 0, 'clicked': 0, 'ctr': 0.0})
        self.logger.debug(f"Thuộc tính self.category_performance được tạo: {type(self.category_performance)}")
        if hasattr(self, 'category_performance'): self.logger.debug("Xác nhận category_performance tồn tại ngay sau khi tạo.")
        else: self.logger.critical("LỖI NGAY LẬP TỨC - category_performance KHÔNG tồn tại sau khi tạo trong __init__.")

        self.time_performance = defaultdict(lambda: defaultdict(lambda: {'sent': 0, 'clicked': 0}))
        self.logger.debug(f"Thuộc tính self.time_performance được tạo: {type(self.time_performance)}")
        if hasattr(self, 'time_performance'): self.logger.debug("Xác nhận time_performance tồn tại ngay sau khi tạo.")
        else: self.logger.critical("LỖI NGAY LẬP TỨC - time_performance KHÔNG tồn tại sau khi tạo trong __init__.")
        
        self.template_ids: List[str] = []
        self.n_arms: int = 0
        self.n_features: int = self.feature_extractor.get_feature_count()
        self.A: List[np.ndarray] = []
        self.b: List[np.ndarray] = []
        self.arm_performance: Dict[str, Dict[str, Any]] = defaultdict(lambda: {'sent': 0, 'clicked': 0, 'ctr': 0.0})
        self.logger.info(f"Bandit khởi tạo hoàn tất (chưa khởi tạo nhánh cụ thể).")

    def _initialize_arms(self):
        self.logger.info(f"_initialize_arms: Bắt đầu. category_performance tồn tại: {hasattr(self, 'category_performance')}, time_performance tồn tại: {hasattr(self, 'time_performance')}")
        self.template_ids = list(self.content_manager.templates.keys())
        self.n_arms = len(self.template_ids)
        if self.n_features != self.feature_extractor.get_feature_count():
             self.logger.warning(f"Số lượng feature không nhất quán! FeatureExtractor: {self.feature_extractor.get_feature_count()}, Bandit: {self.n_features}. Đang cập nhật bandit.")
             self.n_features = self.feature_extractor.get_feature_count()

        self.A = [np.eye(self.n_features) for _ in range(self.n_arms)]
        self.b = [np.zeros(self.n_features) for _ in range(self.n_arms)]
        self.arm_performance = defaultdict(lambda: {'sent': 0, 'clicked': 0, 'ctr': 0.0})
        self.logger.info(f"_initialize_arms: Hoàn thành. Đã tạo {self.n_arms} nhánh. category_performance tồn tại: {hasattr(self, 'category_performance')}, time_performance tồn tại: {hasattr(self, 'time_performance')}")

    def select_notification(self, uc: UserContext, cand_temps: Optional[List[NotificationTemplate]] = None) -> Optional[Tuple[NotificationTemplate, float]]:
        self.logger.debug(f"select_notification cho người dùng {uc.user_id}. Số template_ids hiện tại: {len(self.template_ids)}")
        if not self.template_ids:
             self.logger.warning("select_notification: Không có mẫu nào để chọn (template_ids rỗng).")
             return None, 0.0
        
        c_temps = cand_temps if cand_temps is not None else self.content_manager.get_suitable_notifications(uc)
        if not c_temps:
            self.logger.debug(f"Không có mẫu phù hợp ban đầu cho {uc.user_id}, đang thử fallback SFW.")
            c_temps = [t for t in self.content_manager.templates.values() if t.nsfw_level <= uc.notification_preferences.nsfw_tolerance]
            if not c_temps:
                self.logger.warning(f"Không có mẫu SFW fallback nào cho {uc.user_id}.")
                return None, 0.0
        
        best_t = None; best_s = -float('inf')
        feats = self.feature_extractor.extract_features(uc)
        curr_t_map = {tid: i for i, tid in enumerate(self.template_ids)}

        for t in c_temps:
            arm_idx = -1
            if t.notification_id not in curr_t_map:
                try: arm_idx = self.template_ids.index(t.notification_id)
                except ValueError: self.logger.warning(f"Mẫu {t.notification_id} không có trong template_ids của bandit khi chọn."); continue
            else: arm_idx = curr_t_map[t.notification_id]
            
            if arm_idx >= len(self.A) or arm_idx < 0: 
                self.logger.error(f"Chỉ số nhánh không hợp lệ {arm_idx} cho mẫu {t.notification_id}"); continue
            
            try:
                A_inv = np.linalg.inv(self.A[arm_idx])
                theta = A_inv @ self.b[arm_idx]
                mr = feats @ theta; cr = self.alpha * np.sqrt(feats @ A_inv @ feats.T)
                ucb = mr + cr + t.engagement_score * 0.2
                if t.category in uc.notification_preferences.preferred_categories: ucb += 0.3
                if uc.hour_of_day in uc.notification_preferences.preferred_hours: ucb += 0.2
                if ucb > best_s: best_s = ucb; best_t = t
            except np.linalg.LinAlgError:
                self.logger.error(f"Lỗi LinAlgError (có thể ma trận A suy biến) cho nhánh {arm_idx} (mẫu {t.notification_id}). Bỏ qua nhánh này.", exc_info=False) # exc_info=False để tránh quá nhiều log
                # Có thể reset nhánh này: self.A[arm_idx] = np.eye(self.n_features); self.b[arm_idx] = np.zeros(self.n_features)
                continue
            except Exception as e:
                self.logger.error(f"Lỗi không mong muốn khi tính UCB cho nhánh {arm_idx} (mẫu {t.notification_id}): {e}", exc_info=True)
                continue

        if best_t is None and c_temps:
            self.logger.debug(f"Không tìm thấy mẫu tốt nhất qua UCB cho {uc.user_id}, chọn ngẫu nhiên từ các ứng viên.")
            best_t = random.choice(c_temps); best_s = 0.0
        
        self.logger.debug(f"Đã chọn mẫu '{best_t.notification_id if best_t else 'None'}' cho {uc.user_id} với điểm {best_s:.2f}")
        return best_t, best_s

    def update(self, template: NotificationTemplate, user_context: UserContext, clicked: bool):
        if not hasattr(self, 'category_performance'):
            self.logger.critical("CẬP NHẬT BANDIT: 'category_performance' BỊ THIẾU! Đang khởi tạo lại."); self.category_performance = defaultdict(lambda: {'sent': 0, 'clicked': 0, 'ctr': 0.0})
        if not hasattr(self, 'time_performance'):
            self.logger.critical("CẬP NHẬT BANDIT: 'time_performance' BỊ THIẾU! Đang khởi tạo lại."); self.time_performance = defaultdict(lambda: defaultdict(lambda: {'sent': 0, 'clicked': 0}))

        self.logger.debug(f"Cập nhật bandit cho mẫu {template.notification_id}, người dùng {user_context.user_id}, đã nhấp: {clicked}")
        reward = 1.0 if clicked else 0.0
        try:
            arm_idx = self.template_ids.index(template.notification_id)
            if arm_idx < len(self.A):
                features = self.feature_extractor.extract_features(user_context)
                self.A[arm_idx] += np.outer(features, features); self.b[arm_idx] += reward * features
            else:
                self.logger.warning(f"Cập nhật bandit: Chỉ số nhánh {arm_idx} không hợp lệ cho mẫu {template.notification_id} khi cập nhật trọng số.")
        except ValueError:
             self.logger.warning(f"Cập nhật bandit: Mẫu {template.notification_id} không tìm thấy trong template_ids. Không cập nhật trọng số bandit.")
        except Exception as e:
            self.logger.error(f"Lỗi không mong muốn khi cập nhật trọng số bandit cho mẫu {template.notification_id}: {e}", exc_info=True)

        self.arm_performance[template.notification_id]['sent'] += 1
        if clicked: self.arm_performance[template.notification_id]['clicked'] += 1
        if self.arm_performance[template.notification_id]['sent'] > 0:
             self.arm_performance[template.notification_id]['ctr'] = self.arm_performance[template.notification_id]['clicked'] / self.arm_performance[template.notification_id]['sent']

        cat_str = template.category
        self.category_performance[cat_str]['sent'] += 1
        if clicked: self.category_performance[cat_str]['clicked'] += 1
        if self.category_performance[cat_str]['sent'] > 0:
            self.category_performance[cat_str]['ctr'] = self.category_performance[cat_str]['clicked'] / self.category_performance[cat_str]['sent']

        hour = user_context.hour_of_day
        self.time_performance[hour][template.notification_id]['sent'] += 1
        if clicked: self.time_performance[hour][template.notification_id]['clicked'] += 1
        self.logger.debug(f"Hoàn thành cập nhật bandit cho mẫu {template.notification_id}.")

    def ensure_arms_updated(self, content_manager: NotificationContentManager):
        self.logger.info(f"ensure_arms_updated: Bắt đầu. category_performance tồn tại: {hasattr(self, 'category_performance')}, time_performance tồn tại: {hasattr(self, 'time_performance')}")
        self.content_manager = content_manager
        new_template_ids = list(self.content_manager.templates.keys())
        needs_update = not self.template_ids or set(self.template_ids) != set(new_template_ids)

        if needs_update:
            self.logger.info("ensure_arms_updated: Phát hiện thay đổi mẫu hoặc khởi tạo lần đầu. Đang đồng bộ hóa các nhánh bandit.")
            self.logger.debug(f"ensure_arms_updated: TRƯỚC _initialize_arms. category_performance tồn tại: {hasattr(self, 'category_performance')}, time_performance tồn tại: {hasattr(self, 'time_performance')}")
            self._initialize_arms()
            self.logger.debug(f"ensure_arms_updated: SAU _initialize_arms. category_performance tồn tại: {hasattr(self, 'category_performance')}, time_performance tồn tại: {hasattr(self, 'time_performance')}")
            self.logger.info(f"ensure_arms_updated: Đã khởi tạo lại/đồng bộ {self.n_arms} nhánh.")
        else:
            self.logger.info(f"ensure_arms_updated: Không có thay đổi mẫu. category_performance tồn tại: {hasattr(self, 'category_performance')}, time_performance tồn tại: {hasattr(self, 'time_performance')}")

class EnhancedFeatureExtractor:
    def __init__(self):
        self.logger = logging.getLogger(self.__class__.__name__)
        self.sub_enc = {"free": 0, "premium": 1, "premium_plus": 2}
        self.freq_enc = {"low": 0, "medium": 1, "high": 2}
        self.mood_enc = {"sad": 0, "neutral": 1, "happy": 2, "excited": 3, "lonely": 4, "bored": 0, "stressed":0}
        self.rel_enc = {"single": 0, "dating": 1, "relationship": 2, "complicated": 3, "exploring": 1}
        self._feature_count = 25 
        self.logger.debug("EnhancedFeatureExtractor đã khởi tạo.")

    def get_feature_count(self) -> int: return self._feature_count
    def extract_features(self, uc: UserContext) -> np.ndarray:
        fts = []; pr = uc.notification_preferences
        fts.append(self.sub_enc.get(uc.subscription_status, 0) / 2.0)
        fts.append(min(uc.gifts_given / 10.0, 1.0)); fts.append(min(uc.diamonds_spent / 1000.0, 1.0))
        fts.append(min(uc.num_chat_characters / 10.0, 1.0)); fts.append(min(uc.messages_per_day / 50.0, 1.0))
        fts.append(uc.notification_click_rate); fts.append(min(max(uc.age - 18, 0) / (65-18), 1.0))
        fts.append(1.0 if uc.gender.lower() == 'female' else 0.0)
        fts.append(self.rel_enc.get(uc.interests.lower(), 0) / 3.0)
        fts.append(uc.day_of_week / 6.0); fts.append(uc.hour_of_day / 23.0)
        fts.append(self.freq_enc.get(pr.notification_frequency.lower(), 1) / 2.0)
        fts.append(len(pr.preferred_hours) / 24.0); fts.append(len(pr.preferred_days) / 7.0)
        fts.append(pr.nsfw_tolerance / 3.0)
        max_cat_c = len(KNOWN_NOTIFICATION_CATEGORIES) if KNOWN_NOTIFICATION_CATEGORIES else 12
        fts.append(len(pr.preferred_categories) / max(1, max_cat_c))
        fts.append(uc.recent_activity_level); fts.append(min(len(uc.recent_chat_characters) / 5.0, 1.0))
        fts.append(1.0 if uc.recent_chat_characters else 0.0)
        fts.append(uc.cumulative_clicks / max(1.0, uc.cumulative_notifications))
        fts.append(self.mood_enc.get(uc.current_mood.lower(), 1) / 4.0)
        hrs_online = max(0, (uc.date - uc.last_online_time).total_seconds() / 3600)
        fts.append(min(hrs_online / 72.0, 1.0))
        fts.extend([float(uc.hour_of_day in pr.preferred_hours), float(uc.day_of_week in pr.preferred_days)])
        fts.append(float(uc.day_of_week >= 5))
        curr_len = len(fts)
        if curr_len > self._feature_count: fts = fts[:self._feature_count]
        elif curr_len < self._feature_count: fts.extend([0.0] * (self._feature_count - curr_len))
        return np.array(fts)

class NotificationRecommendationSystem:
    def __init__(self, notification_json_path: str):
        self.logger = logging.getLogger(self.__class__.__name__)
        self.logger.info("Khởi tạo NotificationRecommendationSystem.")
        self.content_manager = NotificationContentManager(notification_json_path)
        self.logger.info(f"ContentManager được tạo với {len(self.content_manager.templates)} templates.")
        self.bandit = EnhancedNotificationBandit(self.content_manager)
        self.logger.info(f"Bandit được tạo. category_performance tồn tại: {hasattr(self.bandit, 'category_performance')}, time_performance tồn tại: {hasattr(self.bandit, 'time_performance')}")
        self.bandit.ensure_arms_updated(self.content_manager)
        self.logger.info(f"Sau ensure_arms_updated. category_performance tồn tại: {hasattr(self.bandit, 'category_performance')}, time_performance tồn tại: {hasattr(self.bandit, 'time_performance')}")
        self.total_sent = 0; self.total_clicked = 0
        self.user_metrics = defaultdict(lambda: {'sent': 0, 'clicked': 0, 'ctr': 0.0})
        self.logger.info("NotificationRecommendationSystem khởi tạo hoàn tất.")

    def reload_notifications(self, notification_json_path: str):
        self.logger.info(f"Bắt đầu tải lại thông báo từ {notification_json_path}.")
        try:
            self.content_manager = NotificationContentManager(notification_json_path)
            self.logger.info(f"ContentManager mới được tạo với {len(self.content_manager.templates)} templates.")
            if self.bandit: self.logger.debug(f"TRƯỚC ensure_arms_updated khi reload. category_performance tồn tại: {hasattr(self.bandit, 'category_performance')}")
            else: self.logger.warning("TRƯỚC ensure_arms_updated khi reload. Bandit chưa được tạo.")
            self.bandit.ensure_arms_updated(self.content_manager)
            self.logger.debug(f"SAU ensure_arms_updated khi reload. category_performance tồn tại: {hasattr(self.bandit, 'category_performance')}")
            self.logger.info(f"Đã tải lại {len(self.content_manager.templates)} mẫu.")
        except Exception as e:
            self.logger.critical(f"LỖI NGHIÊM TRỌNG khi tải lại thông báo: {e}", exc_info=True)

    def get_recommendation(self, uc: UserContext) -> Optional[Dict[str, Any]]:
        self.logger.debug(f"get_recommendation cho người dùng {uc.user_id}")
        t, conf = self.bandit.select_notification(uc)
        if not t:
            self.logger.info(f"Không có đề xuất nào cho người dùng {uc.user_id}")
            return None
        title, content = self.content_manager.personalize_notification(t, uc)
        self.logger.info(f"Đề xuất cho người dùng {uc.user_id}: Mẫu {t.notification_id}, Tiêu đề '{title[:30]}...'")
        return {'template_id': t.notification_id, 'title': title, 'content': content, 'category': t.category,
                'notification_type': t.notification_type, 'nsfw_level': t.nsfw_level,
                'confidence_score': conf if conf is not None else 0.0, 'expected_engagement': t.engagement_score,
                'recommended_time': uc.date, 'user_id': uc.user_id}

    def record_interaction(self, rec: Dict[str, Any], clicked: bool, uc_update: UserContext):
        tid = rec['template_id']
        self.logger.debug(f"Ghi lại tương tác cho người dùng {rec['user_id']}, mẫu {tid}, đã nhấp: {clicked}")
        if tid not in self.content_manager.templates:
            self.logger.error(f"Mẫu ID {tid} từ đề xuất không tìm thấy trong ContentManager khi ghi lại tương tác.")
            return
        t = self.content_manager.templates[tid]
        self.bandit.update(t, uc_update, clicked)
        self.total_sent += 1
        if clicked: self.total_clicked += 1
        uid = rec['user_id']
        self.user_metrics[uid]['sent'] += 1
        if clicked: self.user_metrics[uid]['clicked'] += 1
        if self.user_metrics[uid]['sent'] > 0: self.user_metrics[uid]['ctr'] = self.user_metrics[uid]['clicked'] / self.user_metrics[uid]['sent']
        self.logger.debug(f"Hoàn thành ghi lại tương tác cho mẫu {tid}.")

    def get_performance_report(self) -> Dict[str, Any]:
        self.logger.info("Đang tạo báo cáo hiệu suất.")
        ov_ctr = (self.total_clicked / self.total_sent) if self.total_sent > 0 else 0.0
        cat_stats = {}
        bandit_cat_perf = self.bandit.category_performance if hasattr(self.bandit, 'category_performance') else {}
        for cat_s, stats in bandit_cat_perf.items():
            cat_stats[cat_s] = {'sent': stats['sent'], 'clicked': stats['clicked'], 'ctr': stats['ctr'] if stats['sent'] > 0 else 0.0}
        
        bandit_arm_perf = self.bandit.arm_performance if hasattr(self.bandit, 'arm_performance') else {}
        valid_arm_perf = {arm_id: stats for arm_id, stats in bandit_arm_perf.items() if arm_id in self.content_manager.templates}
        top_ts = sorted(valid_arm_perf.items(), key=lambda x: x[1]['ctr'] if x[1]['sent'] > 0 else -1, reverse=True)[:10]
        
        report_data = {'overall_ctr': ov_ctr, 'total_sent': self.total_sent, 'total_clicked': self.total_clicked,
                       'category_performance': cat_stats,
                       'top_templates': [{'template_id': tid, 'type': self.content_manager.templates[tid].notification_type,
                                          'category': self.content_manager.templates[tid].category,
                                          'title': self.content_manager.templates[tid].title[:60] + "...",
                                          'ctr': s['ctr'], 'sent': s['sent'], 'clicked': s['clicked']} for tid, s in top_ts]}
        self.logger.info("Tạo báo cáo hiệu suất hoàn thành.")
        return report_data

logger_main = logging.getLogger("MAIN_SIMULATION")

def create_sample_user_context(uid: str, avail_cats: List[str]) -> UserContext:
    now = datetime.now()
    safe_cs = avail_cats if avail_cats else list(KNOWN_NOTIFICATION_CATEGORIES)
    num_pref = random.randint(0, min(5, len(safe_cs)))
    pref_cs = random.sample(safe_cs, num_pref) if num_pref > 0 and safe_cs else []
    rem_block = [c for c in safe_cs if c not in pref_cs]
    num_block = random.randint(0, min(2, len(rem_block)))
    block_cs = random.sample(rem_block, num_block) if num_block > 0 and rem_block else []
    return UserContext(
        user_id=uid, date=now, subscription_status=random.choice(['free', 'premium']),
        interests=random.choice(['single', 'dating']), gender=random.choice(['male', 'female']),
        age=random.randint(18, 55), gifts_given=random.randint(0, 5), diamonds_spent=random.uniform(0, 100),
        num_chat_characters=random.randint(0, 3), messages_per_day=random.uniform(0, 20),
        notification_click_rate=random.uniform(0.05, 0.5),
        recent_chat_characters=random.sample(['Emma', 'Alex', 'Sophia'], random.randint(0, 3)),
        top_chat_characters=['Emma', 'Alex'] if random.random() > 0.5 else [],
        online_time_periods=[(now - timedelta(hours=random.randint(2,5)), now - timedelta(hours=random.randint(0,1)))],
        last_online_time=now - timedelta(hours=random.uniform(0.5, 48)),
        day_of_week=now.weekday(), hour_of_day=now.hour, cumulative_clicks=random.randint(5, 100),
        cumulative_notifications=random.randint(50, 300), recent_activity_level=random.uniform(0.0, 1.0),
        preferred_character_types=['friendly', 'romantic'], current_mood=random.choice(['happy', 'lonely', 'neutral']),
        notification_preferences=UserNotificationPreferences(
            user_id=uid, preferred_hours=sorted(random.sample(range(24), random.randint(3, 6))),
            preferred_days=sorted(random.sample(range(7), random.randint(2, 5))),
            max_notifications_per_day=random.randint(1, 4), time_zone_offset=random.choice([-7, 0, 5]),
            do_not_disturb_start=random.choice([22, 23, 0]), do_not_disturb_end=random.choice([6, 7, 8]),
            notification_frequency=random.choice(['low', 'medium', 'high']), nsfw_tolerance=random.randint(0, 3),
            preferred_categories=pref_cs, blocked_categories=block_cs))

def simulate_system_usage(system: NotificationRecommendationSystem, num_users: int, num_days: int, updated_json_path: Optional[str] = None):
    logger_main.info(f"🚀 Bắt đầu mô phỏng với {num_users} người dùng trong {num_days} ngày...")
    loaded_cats = system.content_manager.get_all_categories()
    if not loaded_cats:
        loaded_cats = list(KNOWN_NOTIFICATION_CATEGORIES)
        logger_main.warning("Mô phỏng: Không có danh mục nào được tải, sử dụng danh mục mặc định đã biết.")
    users_ctxs = [create_sample_user_context(f"user_{i:03d}", loaded_cats) for i in range(num_users)]

    for day in range(num_days):
        logger_main.info(f"📅 Ngày {day + 1} của mô phỏng")
        random.shuffle(users_ctxs)
        for idx, uc in enumerate(users_ctxs):
            uc.date = datetime.now() + timedelta(days=day, hours=random.uniform(-2,2))
            uc.day_of_week = uc.date.weekday(); uc.hour_of_day = uc.date.hour
            uc.current_mood = random.choice(['happy', 'lonely', 'neutral'])
            uc.recent_activity_level = max(0, min(1, uc.recent_activity_level + random.uniform(-0.1, 0.1)))
            rec = system.get_recommendation(uc)
            if rec:
                prob = 0.1 + rec['expected_engagement'] * 0.2
                if rec['category'] in uc.notification_preferences.preferred_categories: prob += 0.15
                if rec['category'] in uc.notification_preferences.blocked_categories: prob -= 0.2
                if rec['nsfw_level'] <= uc.notification_preferences.nsfw_tolerance: prob += 0.1
                else: prob -= 0.15
                if uc.hour_of_day in uc.notification_preferences.preferred_hours: prob += 0.05
                clicked = random.random() < max(0.01, min(prob, 0.9))
                system.record_interaction(rec, clicked, uc)
                if day == 0 and idx < 2 :
                    logger_main.debug(f"  👤 {uc.user_id} -> Rec: '{rec['title'][:30]}...' (Cat: {rec['category']}) -> Clicked: {clicked} (P:{prob:.2f})")
            else:
                logger_main.debug(f"Không có đề xuất nào cho người dùng {uc.user_id} vào ngày {day+1}, giờ {uc.hour_of_day}.")

        if day == 0 and updated_json_path and Path(updated_json_path).exists():
            logger_main.info(f"\n💡 Mô phỏng cập nhật mẫu từ '{updated_json_path}'...")
            system.reload_notifications(updated_json_path)
    logger_main.info("Mô phỏng hoàn thành.")
    return system.get_performance_report()

def main():
    setup_logging(level=logging.DEBUG) 
    logger_main.info("🎯 Hệ thống Bandit Thông báo Nâng cao - Bắt đầu")
    logger_main.info("=" * 70)
    json_path = "notification.json"
    updated_json_for_simulation = None

    if not Path(json_path).exists():
        logger_main.critical(f"LỖI: Không tìm thấy tệp dữ liệu '{json_path}'. Vui lòng tạo hoặc đặt tệp này vào cùng thư mục.")
        return

    try:
        logger_main.info(f"Đang tải thông báo từ tệp của bạn: '{json_path}'")
        system = NotificationRecommendationSystem(json_path)
        if not system.content_manager.templates:
            logger_main.warning(f"CẢNH BÁO: Không có mẫu nào được tải từ '{json_path}'. Báo cáo hiệu suất có thể trống.")
        else:
            logger_main.info(f"✅ Đã tải {len(system.content_manager.templates)} mẫu từ '{json_path}'.")
        logger_main.info(f"📊 Danh mục ban đầu: {system.content_manager.get_all_categories()}")

        report = simulate_system_usage(system, num_users=10, num_days=2, updated_json_path=updated_json_for_simulation)

        logger_main.info(f"\n📈 BÁO CÁO HIỆU SUẤT (sau {system.total_sent} lượt gửi)")
        logger_main.info("=" * 70)
        logger_main.info(f"CTR Tổng thể: {report['overall_ctr']:.2%}")
        logger_main.info(f"Tổng gửi: {report['total_sent']}, Tổng nhấp: {report['total_clicked']}")
        logger_main.info(f"\n🏆 MẪU HOẠT ĐỘNG HIỆU QUẢ NHẤT:")
        if report['top_templates']:
            for i, ti in enumerate(report['top_templates'], 1):
                logger_main.info(f"  {i}. ID: {ti['template_id']} (Loại: {ti['type']}, Cat: {ti['category']})")
                logger_main.info(f"     Tiêu đề: {ti['title']}")
                logger_main.info(f"     CTR: {ti['ctr']:.2%} ({ti['clicked']}/{ti['sent']})")
        else: logger_main.info("  Không có mẫu nào trong top templates.")

        logger_main.info(f"\n📊 HIỆU SUẤT DANH MỤC:")
        if report['category_performance']:
            sorted_cats = sorted(report['category_performance'].items(), key=lambda item: item[1]['ctr'] if item[1]['sent'] > 0 else -1, reverse=True)
            for cat, stats in sorted_cats:
                if stats['sent'] > 0: logger_main.info(f"  {cat:<25}: {stats['ctr']:.2%} CTR ({stats['clicked']}/{stats['sent']})")
        else: logger_main.info("  Không có dữ liệu hiệu suất danh mục.")

        logger_main.info(f"\n🔄 Danh mục cuối cùng: {system.content_manager.get_all_categories()}")
        logger_main.info(f"\n✅ Trình diễn hệ thống hoàn tất!")
    except FileNotFoundError:
        logger_main.critical(f"LỖI FileNotFoundError: Không thể tìm thấy tệp JSON cần thiết (có thể là '{json_path}' hoặc tệp cập nhật).", exc_info=True)
    except Exception as e:
        logger_main.critical(f"❌ Lỗi không mong muốn trong quá trình chạy chính: {str(e)}", exc_info=True)

if __name__ == "__main__":
    main()