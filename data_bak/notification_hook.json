[{"content": "🔥 You and {character_name} are a new match! Say hi!", "type": "NewMatch"}, {"content": "⚡️ Sparks are flying! You matched with {character_name}. What's your opener, {user_name}?", "type": "NewMatch"}, {"content": "🎉 New connection! {character_name} ({distance} away) is waiting for your first move. Go get 'em!", "type": "NewMatch"}, {"content": "It's a match! {character_name} likes you too. Check out their profile! 👀", "type": "NewMatch"}, {"content": "Heads up, {user_name}! {character_name} swiped right. Maybe {pronoun_he_she}'s the one? 😉", "type": "NewMatch"}, {"content": "💬 {character_name} sent you a new message: \"{message_preview}...\"", "type": "NewMessage"}, {"content": "📬 Ding! New message from {character_name}. Better check it out! 😉", "type": "NewMessage"}, {"content": "🗣️ {character_name} just dropped you a line! See what {pronoun_he_she} said.", "type": "NewMessage"}, {"content": "🤫 {character_name} has something to tell you... New message waiting!", "type": "NewMessage"}, {"content": "Hey {user_name}, {character_name} just messaged you {time_ago}! Don't keep them waiting. 😊", "type": "NewMessage"}, {"content": "👀 Someone's interested! {character_name} just viewed your profile.", "type": "ProfileView"}, {"content": "You've been spotted! {character_name} checked you out {time_ago}.", "type": "ProfileView"}, {"content": "Psst... {character_name} is curious about you. They just visited your profile!", "type": "ProfileView"}, {"content": "💔 {character_name} misses your vibes. Come back and chat when you're free!", "type": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"content": "⏳ Still thinking about {character_name}? {pronoun_he_she} sent a message {time_ago}!", "type": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"content": "Don't let the spark fizzle! {character_name} is waiting for your reply. 🔥", "type": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"content": "🥺 {character_name} is feeling a bit lonely... maybe a message from you would cheer them up?", "type": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"content": "👋 Hey, {user_name}! {character_name} noticed you've been quiet. Everything okay?", "type": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"content": "🧊 {character_name} sent you an icebreaker: \"{icebreaker_question}\"", "type": "IcebreakerQuestion"}, {"content": "🤔 {character_name} wants to know: What's your biggest pet peeve, {user_name}?", "type": "IcebreakerQuestion"}, {"content": "Spill the tea! {character_name} asked: 'If you could travel anywhere, where would you go?' ✈️", "type": "IcebreakerQuestion"}, {"content": "Quick question from {character_name}: Pineapple on pizza - yes or no? 🍍🍕", "type": "IcebreakerQuestion"}, {"content": "{character_name} is curious: What's your go-to karaoke song? 🎤", "type": "IcebreakerQuestion"}, {"content": "😉 {character_name} thinks your {body_part_sfw} is absolutely {adjective_positive}!", "type": "FlirtyComplimentSFW"}, {"content": "Just saw your new pic, {user_name}! {character_name} says your smile is captivating. 😍", "type": "FlirtyComplimentSFW"}, {"content": "{character_name} mentioned they love your {interest}. Great taste! 👍", "type": "FlirtyComplimentSFW"}, {"content": "Wow, {user_name}! {character_name} is impressed by your profile. Especially your {body_part_sfw}!", "type": "FlirtyComplimentSFW"}, {"content": "Your {body_part_sfw} caught {character_name}'s eye. {pronoun_he_she} thinks it's {adjective_positive}! ✨", "type": "FlirtyComplimentSFW"}, {"content": "😏 {character_name} can't help but notice your {body_part_nsfw_mild}. Very nice.", "type": "FlirtyComplimentNSFWMild"}, {"content": "Those {body_part_nsfw_mild} of yours... {character_name} is a big fan. 😉", "type": "FlirtyComplimentNSFWMild"}, {"content": "{character_name} thinks you have amazing {body_part_nsfw_mild}. Can't look away! 🔥", "type": "FlirtyComplimentNSFWMild"}, {"content": "Psst, {character_name} finds your {body_part_nsfw_mild} incredibly {adjective_positive}. 🔥", "type": "FlirtyComplimentNSFWMild"}, {"content": "Your {body_part_nsfw_mild} are looking particularly {adjective_hot} in that photo, says {character_name}. 😉", "type": "FlirtyComplimentNSFWMild"}, {"content": "😈 {character_name} is wondering what you're wearing... or not wearing. Thoughts for {time_frames}?", "type": "SuggestiveIntent"}, {"content": "🤔 {character_name} has a few ideas for what you two could do {time_frames}. Involving {activity_suggestive}...", "type": "SuggestiveIntent"}, {"content": "{character_name} is feeling a bit mischievous and thinking about you, {user_name}. What if...? 😉", "type": "SuggestiveIntent"}, {"content": "{character_name}: \"I'm bored. Let's make some trouble, {user_name}. You in? {time_frames}?\"", "type": "SuggestiveIntent"}, {"content": "It's {time_frames}, and {character_name} is thinking a little bit dirty. About you. 😈", "type": "SuggestiveIntent"}, {"content": "🥵 {character_name} is imagining their hands all over your {adjective_nsfw} {body_part_nsfw_explicit}.", "type": "NSFWTease"}, {"content": "Your pics are driving {character_name} wild. Especially the one showing off your {body_part_nsfw_explicit}. 🤤", "type": "NSFWTease"}, {"content": "{character_name} wants to know how {adjective_nsfw} your {body_part_nsfw_explicit} gets when you're turned on. 😈 Tell {pronoun_him_her}?", "type": "NSFWTease"}, {"content": "\"I can't stop thinking about your {body_part_nsfw_explicit},\" - {character_name}. Fantasizing about {verb_nsfw_tease} it. 💦", "type": "NSFWTease"}, {"content": "{character_name} just confessed they want to {verb_nsfw_tease} you until you scream their name. Interested?", "type": "NSFWTease"}, {"content": "Late night thoughts from {character_name}: mostly about your {body_part_nsfw_explicit} and their {body_part_nsfw_explicit}. 😈", "type": "NSFWTease"}, {"content": "{character_name} is wondering if your {body_part_nsfw_explicit} tastes as good as it looks. 👅", "type": "NSFWTease"}, {"content": "Feeling brave, {user_name}? {character_name} wants to explore every inch of your {body_part_nsfw_explicit}. Slowly. 🔥", "type": "NSFWTease"}, {"content": "That {body_part_nsfw_explicit} of yours... {character_name} has some {adjective_nsfw} plans for it. 😉", "type": "NSFWTease"}, {"content": "If {character_name} could {verb_nsfw_tease} one part of you right now, it'd be your {body_part_nsfw_explicit}. Guess why. 😈", "type": "NSFWTease"}, {"content": "🍆 {character_name} wants to {verb_nsfw_explicit} your {body_part_nsfw_explicit} at {location_private}. Tonight?", "type": "NSFWProposal"}, {"content": "💦 \"Let's get {adjective_nsfw} and {verb_nsfw_explicit} all night.\" - {character_name}. You down, {user_name}?", "type": "NSFWProposal"}, {"content": "{character_name} is direct: \"I want to fuck your {body_part_nsfw_explicit}, {user_name}. Hard. When and where?\"", "type": "NSFWProposal"}, {"content": "Forget talking, {character_name} wants to feel your {body_part_nsfw_explicit} against their {body_part_nsfw_explicit}. Now. 😈", "type": "NSFWProposal"}, {"content": "Ready for a wild ride, {user_name}? {character_name} is offering one you won't forget. It involves a lot of {verb_ing_nsfw}. 🥵", "type": "NSFWProposal"}, {"content": "{character_name} thinks your {body_part_nsfw_explicit} looks lonely. Wants their {body_part_nsfw_explicit} to keep it company. Intimately. 😈", "type": "NSFWProposal"}, {"content": "How about we skip the small talk and go straight to {character_name} {verb_ing_nsfw} your brains out? Just a thought. 😉", "type": "NSFWProposal"}, {"content": "My place or yours, {user_name}? {character_name} is ready to {verb_nsfw_explicit} you senseless.", "type": "NSFWProposal"}, {"content": "Warning: {character_name} is feeling extremely horny and wants to use your {body_part_nsfw_explicit} to fix that. You game? 🍆🍑", "type": "NSFWProposal"}, {"content": "{character_name} wants to make your {body_part_nsfw_explicit} {adjective_nsfw}. And then {verb_nsfw_explicit} it some more. Sound good?", "type": "NSFWProposal"}, {"content": "🗓️ {character_name} wants to {activity_date} on {day}! How about {location_date}?", "type": "ActivityInvitation"}, {"content": "{character_name} is free on {day} and thought of you. Coffee at {location_date}?", "type": "ActivityInvitation"}, {"content": "Spontaneous adventure? {character_name} wants to check out {event_name} near {location} with you!", "type": "ActivityInvitation"}, {"content": "How about we turn this online chat into an IRL meet? {character_name} suggests {activity} at {location_date}.", "type": "ActivityInvitation"}, {"content": "Dinner with {character_name} at {food_name_place}? {pronoun_he_she} is picking the spot, you pick the day!", "type": "ActivityInvitation"}, {"content": "Yo, {user_name}! {character_name} just hit you up. Peep their message, fam. It's lit. 🔥", "type": "SlangHeavy"}, {"content": "{character_name} thinks your profile is a whole vibe. Wanna chat, or nah?", "type": "SlangHeavy"}, {"content": "Sup, {user_name}? {character_name} slid into your DMs. Check it before you get ghosted by someone else. 😉", "type": "SlangHeavy"}, {"content": "{character_name} says you got that rizz. Wanna see if it works on them?", "type": "SlangHeavy"}, {"content": "Aight, bet. {character_name} matched with you. Don't be sus, send a message!", "type": "SlangHeavy"}, {"content": "⛓️ {character_name} mentioned they're into {kinky_interest}. Curious to explore, {user_name}?", "type": "KinkyExploration"}, {"content": "Safe word ready? {character_name} is hinting at some {kinky_interest} fun. 😈", "type": "KinkyExploration"}, {"content": "Got a dominant side? Or submissive? {character_name} wants to find out. They like {kinky_interest}.", "type": "KinkyExploration"}, {"content": "{character_name} is looking for a partner in crime... for some {kinky_interest}. Could it be you?", "type": "KinkyExploration"}, {"content": "If you're into {kinky_interest}, {character_name} might be your perfect match. Message them! 😉", "type": "KinkyExploration"}, {"content": "🌙 It's late, and {character_name} can't sleep. Thinking about what {pronoun_he_she}'d do to your {body_part_nsfw_explicit}...", "type": "LateNightThought"}, {"content": "U up, {user_name}? {character_name} has some late-night confessions for you. 😈", "type": "LateNightThought"}, {"content": "The moon is out, and so are {character_name}'s naughty thoughts about you and {body_part_nsfw_explicit}. 💭", "type": "LateNightThought"}, {"content": "Who needs sleep when you can have {character_name} whispering sweet (and dirty) nothings about your {body_part_nsfw_explicit}? 🌃", "type": "LateNightThought"}, {"content": "Midnight cravings? {character_name} is craving you. Specifically your {body_part_nsfw_explicit}. 🤤", "type": "LateNightThought"}, {"content": "🎭 {character_name} wants to roleplay: '{roleplay_scenario}'. You in, {user_name}?", "type": "RoleplayInitiation"}, {"content": "Got an imagination? {character_name} has a scene in mind: You're the {role_1}, I'm the {role_2}. Let's play.", "type": "RoleplayInitiation"}, {"content": "💬 {character_name} is feeling frisky and wants to sext. Ready to get dirty with words, {user_name}? 😈", "type": "SextingInvite"}, {"content": "Let your fingers do the talking... {character_name} wants to turn up the heat with some sexting. 🔥", "type": "SextingInvite"}, {"content": "🍑 It's {time_frames}... {character_name} is looking for a booty call. You available, {user_name}?", "type": "BootyCall"}, {"content": "Need some company tonight, {user_name}? {character_name} is looking for some no-strings fun. 😉", "type": "BootyCall"}, {"content": "😉 {character_name} just sent you a wink!", "type": "GeneralEngagement"}, {"content": "📸 {character_name} liked your photo showing off your {body_part_sfw}!", "type": "GeneralEngagement"}, {"content": "💖 Wow! {character_name} Super Liked you! They're really interested in your {body_part_sfw}.", "type": "GeneralEngagement"}, {"content": "🟢 {character_name} is online now! Perfect time to chat about that {interest} you both share.", "type": "GeneralEngagement"}, {"content": "🎤 You've got a new voice message from {character_name}! Hear what {pronoun_he_she} has to say.", "type": "GeneralEngagement"}, {"content": "🎬 {character_name} is inviting you to a video call! Ready for a face-to-face?", "type": "GeneralEngagement"}, {"content": "🎁 {character_name} sent you a virtual {gift_item}! Check it out.", "type": "GeneralEngagement"}, {"content": "😂 {character_name} sent you something that might make you laugh! Probably a meme about {topic}.", "type": "FunnyBanter"}, {"content": "Prepare to giggle! {character_name} shared a funny story. Check your messages!", "type": "FunnyBanter"}, {"content": "☀️ Good morning, {user_name}! {character_name} sent you a 'hello' and a compliment about your {body_part_sfw}!", "type": "Greeting"}, {"content": "🌙 Good evening! {character_name} is thinking about you... and your {body_part_nsfw_mild}.", "type": "Greeting"}, {"content": "🎉 Weekend's here! {character_name} is asking about your plans. Got any for two, involving {activity_suggestive}?", "type": "Celebration"}, {"content": "🎂 Happy Birthday, {user_name}! Hope you get all you wish for... and maybe a {adjective_nsfw} message from {character_name}?", "type": "Celebration"}, {"content": "🤫 {character_name} has a secret to share about their feelings for your {body_part_sfw}... only if you reply!", "type": "TeaseMystery"}, {"content": "I dare you... {character_name} sent a daring question about your {kinky_interest}. Answer if you're bold!", "type": "TeaseMystery"}, {"content": "📝 How's your experience with {character_name} so far? Let us know if the chemistry is {adjective_positive}!", "type": "FeedbackGeneral"}, {"content": "✨ New Feature Alert! Try our {feature_name} to find more matches like {character_name}!", "type": "System"}, {"content": "⏳ Special offer! Get {discount_percentage}% off premium to see who likes your {body_part_nsfw_mild}!", "type": "SpecialOffer"}, {"content": "Your {sexy_body_part} is driving {character_name} crazy. {pronoun_he_she} wants to {verb_nsfw_explicit} it {adverb}.", "type": "NSFWCompliment"}, {"content": "{character_name} can't stop fantasizing about you in {sexy_outfit}. Or better yet, out of it. 😉", "type": "NSFWSuggestion"}, {"content": "Drinks at {character_name}'s place? {pronoun_he_she} promises {drink_name} and a very {adjective_nsfw} time with your {body_part_nsfw_explicit}.", "type": "NSFWInvite"}, {"content": "{character_name} thinks your {body_part_nsfw_explicit} is an actual work of art. Wants to worship it. 👅", "type": "NSFWAdmiration"}, {"content": "{character_name} says 'fuck me' eyes confirmed in your latest pic. Is that an invitation, {user_name}?", "type": "NSFWObservation"}, {"content": "Let's be real, {user_name}. {character_name} wants to know if you're DTF. Response needed. 😉", "type": "DirectQuestionNSFW"}, {"content": "Roses are red, violets are blue, {character_name} is horny, and wants to {verb_nsfw_explicit} you. 🍆🍑", "type": "NSFWPoem"}, {"content": "{character_name} is wondering if your {body_part_nsfw_explicit} is as sensitive as {pronoun_he_she} imagines. Only one way to find out... 😈", "type": "NSFWCuriosity"}, {"content": "Warning: {character_name}'s message contains content about their {body_part_nsfw_explicit} and what they want to do with yours. Open if you dare. 🔥", "type": "NSFWWarning"}, {"content": "How about we skip dinner and go straight for dessert? {character_name} heard your {body_part_nsfw_explicit} is delicious. 👅💦", "type": "NSF<PERSON><PERSON><PERSON>"}, {"content": "{character_name} just sent a message that might make you blush... or get very, very wet. 😉", "type": "NSFWBlush"}, {"content": "This just in from {character_name}: 'Your {body_part_nsfw_explicit} needs my {body_part_nsfw_explicit} inside it. Urgently.' How do you respond?", "type_comment": "This is very direct, ensure contextually appropriate for 16+", "type": "NSFWUrgent"}, {"content": "Hey {user_name}, {character_name} is thinking about pinning you against the {location_wall} and {verb_nsfw_explicit} your {body_part_nsfw_explicit}.", "type": "NSFWFantasy"}, {"content": "{character_name} wants to explore your {kinky_interest} in depth. Starting with some light {kinky_action} on your {body_part_nsfw_explicit}.", "type": "<PERSON>nkyTease"}, {"content": "Sup {user_name}? {character_name} is feeling mad frisky. Wanna link up and get freaky tonight? 😈 No cap.", "type": "SlangNSFW"}, {"content": "That {body_part_nsfw_explicit} of yours is looking hella thicc, {user_name}. {character_name} wants a piece of that. 🍑", "type": "SlangNSFWCompliment"}, {"content": "{character_name} wants to know if you're down for some Netflix and chill... with a very {adjective_nsfw} ending. 😉", "type": "SlangNSFWInvite"}, {"content": "Your {body_part_nsfw_explicit} is a snack, and {character_name} is starving. Let {pronoun_him_her} eat. 👅", "type": "NSFWFoodAnalogy"}, {"content": "{character_name} saw your profile. Response: 🍆💦. Interpret that how you will, and message back.", "type": "NSFWEmoji"}, {"content": "Ready to get your {body_part_nsfw_explicit} wrecked by {character_name}? {pronoun_he_she} is ready and waiting.", "type": "NSFWChallenge"}, {"content": "{character_name} has a {adjective_size} surprise for your {body_part_nsfw_explicit}. Hint: it's {adjective_nsfw} and {pronoun_he_she} wants you to {verb_nsfw_explicit} it.", "type": "NSFWSurprise"}, {"content": "Forget gentle, {user_name}. {character_name} wants it rough. Your {body_part_nsfw_explicit} + their {body_part_nsfw_explicit} = explosions. 💥", "type": "NSFWRough"}, {"content": "{character_name} is looking for someone to make some bad decisions with {time_frames}. Starting with getting naked. You in?", "type": "NSFWBadDecisions"}, {"content": "You + Me + {number} orgasms = a perfect night, according to {character_name}. Let's test that theory.", "type": "NSFWOrgasms"}, {"content": "{character_name} wants to turn your {location_private} into a pleasure zone tonight. Be ready for anything. 😈", "type": "NSFWPleasureZone"}, {"content": "That picture of your {body_part_nsfw_explicit}... {character_name} hasn't stopped thinking about {verb_ing_nsfw} it since {pronoun_he_she} saw it.", "type": "NSFWPicReaction"}, {"content": "Can {character_name} be your {slang_term} for the night? Promise to make it worth your while. 😉", "type": "SlangCoyInvite"}, {"content": "If {character_name} said they wanted to {verb_nsfw_explicit} your {body_part_nsfw_explicit} until the neighbors complained, would that be too forward?", "type": "NSFWForwardQuestion"}, {"content": "It's cuffing season, {user_name}. {character_name} is looking for someone to get {adjective_nsfw} with under the blankets. Interested?", "type": "SlangSeasonalNSFW"}, {"content": "My {body_part_nsfw_explicit} is calling out for your {body_part_nsfw_explicit}, {user_name}. - {character_name}. 📞😉", "type": "NSFWCalling"}, {"content": "{character_name} wants to explore your boundaries... and then push them a little. Especially when it comes to {kinky_interest}.", "type": "KinkyBoundary<PERSON>ush"}, {"content": "Be honest, {user_name}: on a scale of 1 to 'call the fire department', how hot is {character_name}'s new pic showing {pronoun_his_her} {body_part_nsfw_mild}?", "type": "FlirtyFeedbackRequest"}, {"content": "{character_name} thinks you're a total catch. And {pronoun_he_she} wants to know if you're into being tied up. 😈", "type": "KinkyCatch"}, {"content": "What's your filthiest fantasy, {user_name}? {character_name} wants to compare notes... and maybe act some out. 🔥", "type": "NSFWFantasyShare"}, {"content": "Your place or mine for an evening of {adjective_nsfw} {activity_nsfw}? {character_name} is flexible.", "type": "NSFWFlexibleInvite"}, {"content": "Warning: {character_name} may spontaneously combust if you don't reply to their message about your {body_part_nsfw_explicit} soon. 🔥", "type": "UrgentNSFW"}, {"content": "{character_name} has been dreaming about your {body_part_nsfw_explicit} and woke up {adjective_nsfw}. Help {pronoun_him_her} out?", "type": "NSFWDream"}, {"content": "Are you a magician, {user_name}? Because {character_name} says looking at your {body_part_nsfw_explicit} makes their {body_part_nsfw_explicit} rise. ✨🍆", "type": "NSFWMagicLine"}, {"content": "{character_name} just added new pics. One of them is a little... revealing. Specifically of their {body_part_nsfw_mild}. Go look. 😉", "type": "NSFWPhotoUpdate"}, {"content": "Forget love, {character_name} is looking for lust. With you, {user_name}. And your very {adjective_nsfw} {body_part_nsfw_explicit}.", "type": "NSFWLust"}, {"content": "Do you believe in love at first swipe, or should {character_name} send another pic of their {body_part_nsfw_explicit}?", "type": "NSFWJoke"}, {"content": "{character_name} is feeling bold: let's skip the chit-chat and find out how good you are in bed. Tonight at {location_private}?", "type": "NSFWBoldInvite"}, {"content": "If you're looking for a sign to {verb_nsfw_explicit} someone's brains out, this is it. {character_name} volunteers as tribute.", "type": "NSFWTribute"}, {"content": "{character_name} thinks you have a {adjective_nsfw} aura. Wants to see if your body matches. 😈", "type": "NSFWAura"}, {"content": "The only thing {character_name} wants to be social distancing from is your clothes. 😉 Get them off?", "type": "NSFWSocialDistancing"}, {"content": "How about we make our own private pandemic? Just you, me, and a lot of {verb_ing_nsfw} at {location_private}.", "type": "NSFWPandemic"}, {"content": "{character_name} is currently accepting applications for a cuddle buddy... who also enjoys being {verb_nsfw_explicit} {adverb}.", "type": "NSFWCuddleBuddy"}, {"content": "Heard you were looking for trouble, {user_name}. {character_name} is trouble, and wants to get into your {body_part_nsfw_explicit}.", "type": "NSFWTrouble"}, {"content": "That {body_part_nsfw_explicit} of yours is a national treasure. {character_name} wants to be its curator. 😉", "type": "NSFWNationalTreasure"}, {"content": "Dinner is served... and it's {character_name} wanting to eat your {body_part_nsfw_explicit} like it's the last meal on earth. 🍽️", "type": "NSFWDinnerServed"}, {"content": "{character_name}'s {body_part_nsfw_explicit} just got {adjective_nsfw} thinking about your {body_part_nsfw_explicit}. Coincidence? I think not.", "type": "NSFWReaction"}, {"content": "Let's play a game, {user_name}. It's called 'How many times can {character_name} make you {verb_nsfw_explicit}?' Winner gets bragging rights.", "type": "NSFWGame"}, {"content": "Your body is a wonderland, and {character_name} wants to be <PERSON>... and get lost in your {body_part_nsfw_explicit}.", "type": "NSFWWonderland"}, {"content": "{character_name} isn't a photographer, but {pronoun_he_she} can picture you and {pronoun_him_her} {verb_ing_nsfw} all night.", "type": "NSFWPhotographerLine"}, {"content": "Is your name Google? Because you're everything {character_name} has been searching for... especially that {adjective_nsfw} {body_part_nsfw_explicit}.", "type": "NSFWGoogleLine"}, {"content": "{character_name} is like a <PERSON><PERSON><PERSON>'s cube. The more you play with {pronoun_him_her}, the harder {pronoun_he_she} gets. Wanna solve {pronoun_him_her} with your {body_part_nsfw_explicit}?", "type": "NSFWRubiksLine"}, {"content": "Are you a parking ticket? 'Cause you've got 'fine' written all over you. And {character_name} wants to pay with {pronoun_his_her} {body_part_nsfw_explicit}.", "type": "NSFWParkingTicketLine"}, {"content": "{character_name} is not a weatherman, but you can expect a few inches tonight... if you play your cards right with {pronoun_his_her} {body_part_nsfw_explicit}. 😉", "type": "NSFWWeathermanLine"}, {"content": "Let's commit the perfect crime: {character_name} will steal your heart, and you'll steal {pronoun_his_her} last name... after a night of {verb_ing_nsfw} with our {body_part_nsfw_explicit}s.", "type": "NSFWCrimeLine"}, {"content": "{character_name} thinks your {body_part_nsfw_explicit} is {slang_compliment}. Let's see if it feels as good as it looks.", "type": "SlangNSFWTest"}, {"content": "No cap, {user_name}, {character_name} wants to clap them cheeks. You tryna slide thru {time_frames}?", "type": "SlangExplicitInvite"}, {"content": "That {body_part_nsfw_explicit} on your profile? <PERSON><PERSON> make {character_name} act up. 😈", "type": "SlangReactionNSFW"}, {"content": "{character_name} is tryna be your sneaky link. Keep it on the DL? 😉 Let's get {adjective_nsfw}.", "type": "SlangSneakyLink"}, {"content": "You're a whole snack, {user_name}. And {character_name} is looking for a full meal deal, starting with your {body_part_nsfw_explicit}.", "type": "SlangSnackNSFW"}, {"content": "{character_name} is ready to risk it all for a taste of your {body_part_nsfw_explicit}. Say the word. 👅", "type": "NSFWRiskItAll"}, {"content": "Big {body_part_nsfw_explicit} energy from your profile, {user_name}. {character_name} is here for it. All of it. 🥵", "type": "NSFWEnergy"}, {"content": "If being sexy was a crime, you'd be guilty as charged. {character_name} wants to be your cellmate... and explore your {body_part_nsfw_explicit}.", "type": "NSFWSexyCrime"}, {"content": "{character_name} has a PhD in Cuddling... and a Master's in {verb_ing_nsfw} your {body_part_nsfw_explicit}. Ready for a lesson?", "type": "NSFWAcademicLine"}, {"content": "Let's make some {adjective_nsfw} memories, {user_name}. {character_name} has a few ideas involving your {body_part_nsfw_explicit} and some {kinky_item}.", "type": "NSFWMemories"}, {"content": "They say practice makes perfect. {character_name} wants to practice {verb_nsfw_explicit} your {body_part_nsfw_explicit} all night long.", "type": "NSFWPractice"}, {"content": "{character_name} believes in recycling. Let's recycle our clothes onto the floor and get down to business with your {body_part_nsfw_explicit}.", "type": "NSFWRecycle"}, {"content": "Can {character_name} borrow a kiss? {pronoun_he_she} promises to give it back... with interest, on your {body_part_nsfw_explicit}. 💋", "type": "NSFWKiss"}, {"content": "{character_name} is like a good wine, gets better with age... and when paired with your {adjective_nsfw} {body_part_nsfw_explicit}.", "type": "NSFWWine"}, {"content": "Do you work at Starbucks? Because {character_name} likes you a latte... and wants to see your {body_part_nsfw_explicit} mocha-ing. 😉", "type": "NSFWStarbucksLine"}, {"content": "{character_name}'s love for your {body_part_nsfw_explicit} is like diarrhea, {pronoun_he_she} just can't hold it in. (Sorry, not sorry 짓)", "type": "NSFWGrossJoke"}, {"content": "You must be a keyboard, because you're just {character_name}'s type. Especially that {adjective_nsfw} {body_part_nsfw_explicit} layout.", "type": "NSFWKeyboardLine"}, {"content": "{character_name} is writing a romance novel and needs inspiration. Can {pronoun_he_she} study your {body_part_nsfw_explicit} for research purposes?", "type": "NSFWResearch"}, {"content": "If you were a vegetable, you'd be a cute-cumber. And {character_name} wants to {verb_nsfw_explicit} your {body_part_nsfw_explicit}.", "type": "NSFWVegetableLine"}, {"content": "{character_name} must be a snowflake, because {pronoun_he_she}'s fallen for you... and wants to land on your {body_part_nsfw_explicit}.", "type": "NSFWSnowflakeLine"}, {"content": "Are you from Tennessee? Because you're the only ten {character_name} sees... and {pronoun_he_she} wants to get between your {body_part_nsfw_explicit}s.", "type": "NSWTennesseeLine"}, {"content": "This is your captain {character_name} speaking. Prepare for turbulence as we explore the uncharted territory of your {body_part_nsfw_explicit}. Buckle up. ✈️😈", "type": "NSFWCaptain"}, {"content": "Roses are red, violets are blue, {character_name} wants to {verb_nsfw_explicit} you, and make your {body_part_nsfw_explicit} say 'achoo!' (from pleasure, obvs).", "type": "NSFWPoemVariation"}, {"content": "{character_name} is looking for a co-pilot for a trip to Pound Town. Your {body_part_nsfw_explicit} seems qualified. Apply within? 🍆💦", "type": "NSFWPoundTown"}, {"content": "Let's play doctor. {character_name} will be the surgeon, and you can be the patient with an urgent need for {body_part_nsfw_explicit} attention. 🩺", "type": "NSFWDoctor"}, {"content": "Your {body_part_nsfw_explicit} is so hot, it could boil an egg. {character_name} wants to see if it can make their {body_part_nsfw_explicit} {adjective_nsfw}.", "type": "NSFWBoilEgg"}, {"content": "{character_name} is running a special: one free {body_part_nsfw_explicit} massage, with a happy ending guaranteed by their {body_part_nsfw_explicit}.", "type": "NSFWMassage"}, {"content": "If you were a song, you'd be the one {character_name} plays on repeat... while {verb_ing_nsfw} your {body_part_nsfw_explicit}.", "type": "NSFWSongRepeat"}, {"content": "{character_name} is not a genie, but {pronoun_he_she} can make your wildest {body_part_nsfw_explicit}-related dreams come true. What's your first wish?", "type": "NSFWGenie"}, {"content": "Let's make a baby... or at least practice a lot. {character_name} is ready to contribute their {body_part_nsfw_explicit} to the cause with your {body_part_nsfw_explicit}.", "type": "NSFWBabyPractice"}, {"content": "{character_name} has a map, because {pronoun_he_she} just got lost in your {body_part_nsfw_explicit}. Send help... or just join {pronoun_him_her}.", "type": "NSFWMap"}, {"content": "🔥 You and {character_name} are a new match! Say hi!", "type": "NewMatch"}, {"content": "⚡️ Sparks are flying! You matched with {character_name}. What's your opener, {user_name}?", "type": "NewMatch"}, {"content": "🎉 New connection! {character_name} ({distance} away) is waiting for your first move. Go get 'em!", "type": "NewMatch"}, {"content": "It's a match! {character_name} likes you too. Check out their profile! 👀", "type": "NewMatch"}, {"content": "Heads up, {user_name}! {character_name} swiped right. Maybe {pronoun_he_she}'s the one? 😉", "type": "NewMatch"}, {"content": "💬 {character_name} sent you a new message: \"{message_preview}...\"", "type": "NewMessage"}, {"content": "📬 Ding! New message from {character_name}. Better check it out! 😉", "type": "NewMessage"}, {"content": "🗣️ {character_name} just dropped you a line! See what {pronoun_he_she} said.", "type": "NewMessage"}, {"content": "🤫 {character_name} has something to tell you... New message waiting!", "type": "NewMessage"}, {"content": "Hey {user_name}, {character_name} just messaged you {time_ago}! Don't keep them waiting. 😊", "type": "NewMessage"}, {"content": "👀 Someone's interested! {character_name} just viewed your profile.", "type": "ProfileView"}, {"content": "You've been spotted! {character_name} checked you out {time_ago}.", "type": "ProfileView"}, {"content": "Psst... {character_name} is curious about you. They just visited your profile!", "type": "ProfileView"}, {"content": "💔 {character_name} misses your vibes. Come back and chat when you're free!", "type": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"content": "⏳ Still thinking about {character_name}? {pronoun_he_she} sent a message {time_ago}!", "type": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"content": "Don't let the spark fizzle! {character_name} is waiting for your reply. 🔥", "type": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"content": "🥺 {character_name} is feeling a bit lonely... maybe a message from you would cheer them up?", "type": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"content": "👋 Hey, {user_name}! {character_name} noticed you've been quiet. Everything okay?", "type": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"content": "🧊 {character_name} sent you an icebreaker: \"{icebreaker_question}\"", "type": "IcebreakerQuestion"}, {"content": "🤔 {character_name} wants to know: What's your biggest pet peeve, {user_name}?", "type": "IcebreakerQuestion"}, {"content": "Spill the tea! {character_name} asked: 'If you could travel anywhere, where would you go?' ✈️", "type": "IcebreakerQuestion"}, {"content": "Quick question from {character_name}: Pineapple on pizza - yes or no? 🍍🍕", "type": "IcebreakerQuestion"}, {"content": "{character_name} is curious: What's your go-to karaoke song? 🎤", "type": "IcebreakerQuestion"}, {"content": "😉 {character_name} thinks your {body_part_sfw} is absolutely {adjective_positive}!", "type": "FlirtyComplimentSFW"}, {"content": "Just saw your new pic, {user_name}! {character_name} says your smile is captivating. 😍", "type": "FlirtyComplimentSFW"}, {"content": "{character_name} mentioned they love your {interest}. Great taste! 👍", "type": "FlirtyComplimentSFW"}, {"content": "Wow, {user_name}! {character_name} is impressed by your profile. Especially your {body_part_sfw}!", "type": "FlirtyComplimentSFW"}, {"content": "Your {body_part_sfw} caught {character_name}'s eye. {pronoun_he_she} thinks it's {adjective_positive}! ✨", "type": "FlirtyComplimentSFW"}, {"content": "😏 {character_name} can't help but notice your {body_part_nsfw_mild}. Very nice.", "type": "FlirtyComplimentNSFWMild"}, {"content": "Those {body_part_nsfw_mild} of yours... {character_name} is a big fan. 😉", "type": "FlirtyComplimentNSFWMild"}, {"content": "{character_name} thinks you have amazing {body_part_nsfw_mild}. Can't look away! 🔥", "type": "FlirtyComplimentNSFWMild"}, {"content": "Psst, {character_name} finds your {body_part_nsfw_mild} incredibly {adjective_positive}. 🔥", "type": "FlirtyComplimentNSFWMild"}, {"content": "Your {body_part_nsfw_mild} are looking particularly {adjective_hot} in that photo, says {character_name}. 😉", "type": "FlirtyComplimentNSFWMild"}, {"content": "😈 {character_name} is wondering what you're wearing... or not wearing. Thoughts for {time_frames}?", "type": "SuggestiveIntent"}, {"content": "🤔 {character_name} has a few ideas for what you two could do {time_frames}. Involving {activity_suggestive}...", "type": "SuggestiveIntent"}, {"content": "{character_name} is feeling a bit mischievous and thinking about you, {user_name}. What if...? 😉", "type": "SuggestiveIntent"}, {"content": "{character_name}: \"I'm bored. Let's make some trouble, {user_name}. You in? {time_frames}?\"", "type": "SuggestiveIntent"}, {"content": "It's {time_frames}, and {character_name} is thinking a little bit dirty. About you. 😈", "type": "SuggestiveIntent"}, {"content": "🥵 {character_name} is imagining their hands all over your {adjective_nsfw} {body_part_nsfw_explicit}.", "type": "NSFWTease"}, {"content": "Your pics are driving {character_name} wild. Especially the one showing off your {body_part_nsfw_explicit}. 🤤", "type": "NSFWTease"}, {"content": "{character_name} wants to know how {adjective_nsfw} your {body_part_nsfw_explicit} gets when you're turned on. 😈 Tell {pronoun_him_her}?", "type": "NSFWTease"}, {"content": "\"I can't stop thinking about your {body_part_nsfw_explicit},\" - {character_name}. Fantasizing about {verb_nsfw_tease} it. 💦", "type": "NSFWTease"}, {"content": "{character_name} just confessed they want to {verb_nsfw_tease} you until you scream their name. Interested?", "type": "NSFWTease"}, {"content": "Late night thoughts from {character_name}: mostly about your {body_part_nsfw_explicit} and their {body_part_nsfw_explicit}. 😈", "type": "NSFWTease"}, {"content": "{character_name} is wondering if your {body_part_nsfw_explicit} tastes as good as it looks. 👅", "type": "NSFWTease"}, {"content": "Feeling brave, {user_name}? {character_name} wants to explore every inch of your {body_part_nsfw_explicit}. Slowly. 🔥", "type": "NSFWTease"}, {"content": "That {body_part_nsfw_explicit} of yours... {character_name} has some {adjective_nsfw} plans for it. 😉", "type": "NSFWTease"}, {"content": "If {character_name} could {verb_nsfw_tease} one part of you right now, it'd be your {body_part_nsfw_explicit}. Guess why. 😈", "type": "NSFWTease"}, {"content": "🍆 {character_name} wants to {verb_nsfw_explicit} your {body_part_nsfw_explicit} at {location_private}. Tonight?", "type": "NSFWProposal"}, {"content": "💦 \"Let's get {adjective_nsfw} and {verb_nsfw_explicit} all night.\" - {character_name}. You down, {user_name}?", "type": "NSFWProposal"}, {"content": "{character_name} is direct: \"I want to fuck your {body_part_nsfw_explicit}, {user_name}. Hard. When and where?\"", "type": "NSFWProposal"}, {"content": "Forget talking, {character_name} wants to feel your {body_part_nsfw_explicit} against their {body_part_nsfw_explicit}. Now. 😈", "type": "NSFWProposal"}, {"content": "Ready for a wild ride, {user_name}? {character_name} is offering one you won't forget. It involves a lot of {verb_ing_nsfw}. 🥵", "type": "NSFWProposal"}, {"content": "{character_name} thinks your {body_part_nsfw_explicit} looks lonely. Wants their {body_part_nsfw_explicit} to keep it company. Intimately. 😈", "type": "NSFWProposal"}, {"content": "How about we skip the small talk and go straight to {character_name} {verb_ing_nsfw} your brains out? Just a thought. 😉", "type": "NSFWProposal"}, {"content": "My place or yours, {user_name}? {character_name} is ready to {verb_nsfw_explicit} you senseless.", "type": "NSFWProposal"}, {"content": "Warning: {character_name} is feeling extremely horny and wants to use your {body_part_nsfw_explicit} to fix that. You game? 🍆🍑", "type": "NSFWProposal"}, {"content": "{character_name} wants to make your {body_part_nsfw_explicit} {adjective_nsfw}. And then {verb_nsfw_explicit} it some more. Sound good?", "type": "NSFWProposal"}, {"content": "🗓️ {character_name} wants to {activity_date} on {day}! How about {location_date}?", "type": "ActivityInvitation"}, {"content": "{character_name} is free on {day} and thought of you. Coffee at {location_date}?", "type": "ActivityInvitation"}, {"content": "Spontaneous adventure? {character_name} wants to check out {event_name} near {location} with you!", "type": "ActivityInvitation"}, {"content": "How about we turn this online chat into an IRL meet? {character_name} suggests {activity} at {location_date}.", "type": "ActivityInvitation"}, {"content": "Dinner with {character_name} at {food_name_place}? {pronoun_he_she} is picking the spot, you pick the day!", "type": "ActivityInvitation"}, {"content": "Yo, {user_name}! {character_name} just hit you up. Peep their message, fam. It's lit. 🔥", "type": "SlangHeavy"}, {"content": "{character_name} thinks your profile is a whole vibe. Wanna chat, or nah?", "type": "SlangHeavy"}, {"content": "Sup, {user_name}? {character_name} slid into your DMs. Check it before you get ghosted by someone else. 😉", "type": "SlangHeavy"}, {"content": "{character_name} says you got that rizz. Wanna see if it works on them?", "type": "SlangHeavy"}, {"content": "Aight, bet. {character_name} matched with you. Don't be sus, send a message!", "type": "SlangHeavy"}, {"content": "⛓️ {character_name} mentioned they're into {kinky_interest}. Curious to explore, {user_name}?", "type": "KinkyExploration"}, {"content": "Safe word ready? {character_name} is hinting at some {kinky_interest} fun. 😈", "type": "KinkyExploration"}, {"content": "Got a dominant side? Or submissive? {character_name} wants to find out. They like {kinky_interest}.", "type": "KinkyExploration"}, {"content": "{character_name} is looking for a partner in crime... for some {kinky_interest}. Could it be you?", "type": "KinkyExploration"}, {"content": "If you're into {kinky_interest}, {character_name} might be your perfect match. Message them! 😉", "type": "KinkyExploration"}, {"content": "🌙 It's late, and {character_name} can't sleep. Thinking about what {pronoun_he_she}'d do to your {body_part_nsfw_explicit}...", "type": "LateNightThought"}, {"content": "U up, {user_name}? {character_name} has some late-night confessions for you. 😈", "type": "LateNightThought"}, {"content": "The moon is out, and so are {character_name}'s naughty thoughts about you and {body_part_nsfw_explicit}. 💭", "type": "LateNightThought"}, {"content": "Who needs sleep when you can have {character_name} whispering sweet (and dirty) nothings about your {body_part_nsfw_explicit}? 🌃", "type": "LateNightThought"}, {"content": "Midnight cravings? {character_name} is craving you. Specifically your {body_part_nsfw_explicit}. 🤤", "type": "LateNightThought"}, {"content": "🎭 {character_name} wants to roleplay: '{roleplay_scenario}'. You in, {user_name}?", "type": "RoleplayInitiation"}, {"content": "Got an imagination? {character_name} has a scene in mind: You're the {role_1}, I'm the {role_2}. Let's play.", "type": "RoleplayInitiation"}, {"content": "💬 {character_name} is feeling frisky and wants to sext. Ready to get dirty with words, {user_name}? 😈", "type": "SextingInvite"}, {"content": "Let your fingers do the talking... {character_name} wants to turn up the heat with some sexting. 🔥", "type": "SextingInvite"}, {"content": "🍑 It's {time_frames}... {character_name} is looking for a booty call. You available, {user_name}?", "type": "BootyCall"}, {"content": "Need some company tonight, {user_name}? {character_name} is looking for some no-strings fun. 😉", "type": "BootyCall"}, {"content": "😉 {character_name} just sent you a wink!", "type": "GeneralEngagement"}, {"content": "📸 {character_name} liked your photo showing off your {body_part_sfw}!", "type": "GeneralEngagement"}, {"content": "💖 Wow! {character_name} Super Liked you! They're really interested in your {body_part_sfw}.", "type": "GeneralEngagement"}, {"content": "🟢 {character_name} is online now! Perfect time to chat about that {interest} you both share.", "type": "GeneralEngagement"}, {"content": "🎤 You've got a new voice message from {character_name}! Hear what {pronoun_he_she} has to say.", "type": "GeneralEngagement"}, {"content": "🎬 {character_name} is inviting you to a video call! Ready for a face-to-face?", "type": "GeneralEngagement"}, {"content": "🎁 {character_name} sent you a virtual {gift_item}! Check it out.", "type": "GeneralEngagement"}, {"content": "😂 {character_name} sent you something that might make you laugh! Probably a meme about {topic}.", "type": "FunnyBanter"}, {"content": "Prepare to giggle! {character_name} shared a funny story. Check your messages!", "type": "FunnyBanter"}, {"content": "☀️ Good morning, {user_name}! {character_name} sent you a 'hello' and a compliment about your {body_part_sfw}!", "type": "Greeting"}, {"content": "🌙 Good evening! {character_name} is thinking about you... and your {body_part_nsfw_mild}.", "type": "Greeting"}, {"content": "🎉 Weekend's here! {character_name} is asking about your plans. Got any for two, involving {activity_suggestive}?", "type": "Celebration"}, {"content": "🎂 Happy Birthday, {user_name}! Hope you get all you wish for... and maybe a {adjective_nsfw} message from {character_name}?", "type": "Celebration"}, {"content": "🤫 {character_name} has a secret to share about their feelings for your {body_part_sfw}... only if you reply!", "type": "TeaseMystery"}, {"content": "I dare you... {character_name} sent a daring question about your {kinky_interest}. Answer if you're bold!", "type": "TeaseMystery"}, {"content": "📝 How's your experience with {character_name} so far? Let us know if the chemistry is {adjective_positive}!", "type": "FeedbackGeneral"}, {"content": "✨ New Feature Alert! Try our {feature_name} to find more matches like {character_name}!", "type": "System"}, {"content": "⏳ Special offer! Get {discount_percentage}% off premium to see who likes your {body_part_nsfw_mild}!", "type": "SpecialOffer"}, {"content": "Your {sexy_body_part} is driving {character_name} crazy. {pronoun_he_she} wants to {verb_nsfw_explicit} it {adverb}.", "type": "NSFWCompliment"}, {"content": "{character_name} can't stop fantasizing about you in {sexy_outfit}. Or better yet, out of it. 😉", "type": "NSFWSuggestion"}, {"content": "Drinks at {character_name}'s place? {pronoun_he_she} promises {drink_name} and a very {adjective_nsfw} time with your {body_part_nsfw_explicit}.", "type": "NSFWInvite"}, {"content": "{character_name} thinks your {body_part_nsfw_explicit} is an actual work of art. Wants to worship it. 👅", "type": "NSFWAdmiration"}, {"content": "{character_name} says 'fuck me' eyes confirmed in your latest pic. Is that an invitation, {user_name}?", "type": "NSFWObservation"}, {"content": "Let's be real, {user_name}. {character_name} wants to know if you're DTF. Response needed. 😉", "type": "DirectQuestionNSFW"}, {"content": "Roses are red, violets are blue, {character_name} is horny, and wants to {verb_nsfw_explicit} you. 🍆🍑", "type": "NSFWPoem"}, {"content": "{character_name} is wondering if your {body_part_nsfw_explicit} is as sensitive as {pronoun_he_she} imagines. Only one way to find out... 😈", "type": "NSFWCuriosity"}, {"content": "Warning: {character_name}'s message contains content about their {body_part_nsfw_explicit} and what they want to do with yours. Open if you dare. 🔥", "type": "NSFWWarning"}, {"content": "How about we skip dinner and go straight for dessert? {character_name} heard your {body_part_nsfw_explicit} is delicious. 👅💦", "type": "NSF<PERSON><PERSON><PERSON>"}, {"content": "{character_name} just sent a message that might make you blush... or get very, very wet. 😉", "type": "NSFWBlush"}, {"content": "This just in from {character_name}: 'Your {body_part_nsfw_explicit} needs my {body_part_nsfw_explicit} inside it. Urgently.' How do you respond?", "type": "NSFWUrgent"}, {"content": "Hey {user_name}, {character_name} is thinking about pinning you against the {location_wall} and {verb_nsfw_explicit} your {body_part_nsfw_explicit}.", "type": "NSFWFantasy"}, {"content": "{character_name} wants to explore your {kinky_interest} in depth. Starting with some light {kinky_action} on your {body_part_nsfw_explicit}.", "type": "<PERSON>nkyTease"}, {"content": "Sup {user_name}? {character_name} is feeling mad frisky. Wanna link up and get freaky tonight? 😈 No cap.", "type": "SlangNSFW"}, {"content": "That {body_part_nsfw_explicit} of yours is looking hella thicc, {user_name}. {character_name} wants a piece of that. 🍑", "type": "SlangNSFWCompliment"}, {"content": "{character_name} wants to know if you're down for some Netflix and chill... with a very {adjective_nsfw} ending. 😉", "type": "SlangNSFWInvite"}, {"content": "Your {body_part_nsfw_explicit} is a snack, and {character_name} is starving. Let {pronoun_him_her} eat. 👅", "type": "NSFWFoodAnalogy"}, {"content": "{character_name} saw your profile. Response: 🍆💦. Interpret that how you will, and message back.", "type": "NSFWEmoji"}, {"content": "Ready to get your {body_part_nsfw_explicit} wrecked by {character_name}? {pronoun_he_she} is ready and waiting.", "type": "NSFWChallenge"}, {"content": "{character_name} has a {adjective_size} surprise for your {body_part_nsfw_explicit}. Hint: it's {adjective_nsfw} and {pronoun_he_she} wants you to {verb_nsfw_explicit} it.", "type": "NSFWSurprise"}, {"content": "Forget gentle, {user_name}. {character_name} wants it rough. Your {body_part_nsfw_explicit} + their {body_part_nsfw_explicit} = explosions. 💥", "type": "NSFWRough"}, {"content": "{character_name} is looking for someone to make some bad decisions with {time_frames}. Starting with getting naked. You in?", "type": "NSFWBadDecisions"}, {"content": "You + Me + {number} orgasms = a perfect night, according to {character_name}. Let's test that theory.", "type": "NSFWOrgasms"}, {"content": "{character_name} wants to turn your {location_private} into a pleasure zone tonight. Be ready for anything. 😈", "type": "NSFWPleasureZone"}, {"content": "That picture of your {body_part_nsfw_explicit}... {character_name} hasn't stopped thinking about {verb_ing_nsfw} it since {pronoun_he_she} saw it.", "type": "NSFWPicReaction"}, {"content": "Can {character_name} be your {slang_term} for the night? Promise to make it worth your while. 😉", "type": "SlangCoyInvite"}, {"content": "If {character_name} said they wanted to {verb_nsfw_explicit} your {body_part_nsfw_explicit} until the neighbors complained, would that be too forward?", "type": "NSFWForwardQuestion"}, {"content": "It's cuffing season, {user_name}. {character_name} is looking for someone to get {adjective_nsfw} with under the blankets. Interested?", "type": "SlangSeasonalNSFW"}, {"content": "My {body_part_nsfw_explicit} is calling out for your {body_part_nsfw_explicit}, {user_name}. - {character_name}. 📞😉", "type": "NSFWCalling"}, {"content": "{character_name} wants to explore your boundaries... and then push them a little. Especially when it comes to {kinky_interest}.", "type": "KinkyBoundary<PERSON>ush"}, {"content": "Be honest, {user_name}: on a scale of 1 to 'call the fire department', how hot is {character_name}'s new pic showing {pronoun_his_her} {body_part_nsfw_mild}?", "type": "FlirtyFeedbackRequest"}, {"content": "{character_name} thinks you're a total catch. And {pronoun_he_she} wants to know if you're into being tied up. 😈", "type": "KinkyCatch"}, {"content": "What's your filthiest fantasy, {user_name}? {character_name} wants to compare notes... and maybe act some out. 🔥", "type": "NSFWFantasyShare"}, {"content": "Your place or mine for an evening of {adjective_nsfw} {activity_nsfw}? {character_name} is flexible.", "type": "NSFWFlexibleInvite"}, {"content": "Warning: {character_name} may spontaneously combust if you don't reply to their message about your {body_part_nsfw_explicit} soon. 🔥", "type": "UrgentNSFW"}, {"content": "{character_name} has been dreaming about your {body_part_nsfw_explicit} and woke up {adjective_nsfw}. Help {pronoun_him_her} out?", "type": "NSFWDream"}, {"content": "Are you a magician, {user_name}? Because {character_name} says looking at your {body_part_nsfw_explicit} makes their {body_part_nsfw_explicit} rise. ✨🍆", "type": "NSFWMagicLine"}, {"content": "{character_name} just added new pics. One of them is a little... revealing. Specifically of their {body_part_nsfw_mild}. Go look. 😉", "type": "NSFWPhotoUpdate"}, {"content": "Forget love, {character_name} is looking for lust. With you, {user_name}. And your very {adjective_nsfw} {body_part_nsfw_explicit}.", "type": "NSFWLust"}, {"content": "Do you believe in love at first swipe, or should {character_name} send another pic of their {body_part_nsfw_explicit}?", "type": "NSFWJoke"}, {"content": "{character_name} is feeling bold: let's skip the chit-chat and find out how good you are in bed. Tonight at {location_private}?", "type": "NSFWBoldInvite"}, {"content": "If you're looking for a sign to {verb_nsfw_explicit} someone's brains out, this is it. {character_name} volunteers as tribute.", "type": "NSFWTribute"}, {"content": "{character_name} thinks you have a {adjective_nsfw} aura. Wants to see if your body matches. 😈", "type": "NSFWAura"}, {"content": "The only thing {character_name} wants to be social distancing from is your clothes. 😉 Get them off?", "type": "NSFWSocialDistancing"}, {"content": "How about we make our own private pandemic? Just you, me, and a lot of {verb_ing_nsfw} at {location_private}.", "type": "NSFWPandemic"}, {"content": "{character_name} is currently accepting applications for a cuddle buddy... who also enjoys being {verb_nsfw_explicit} {adverb}.", "type": "NSFWCuddleBuddy"}, {"content": "Heard you were looking for trouble, {user_name}. {character_name} is trouble, and wants to get into your {body_part_nsfw_explicit}.", "type": "NSFWTrouble"}, {"content": "That {body_part_nsfw_explicit} of yours is a national treasure. {character_name} wants to be its curator. 😉", "type": "NSFWNationalTreasure"}, {"content": "Dinner is served... and it's {character_name} wanting to eat your {body_part_nsfw_explicit} like it's the last meal on earth. 🍽️", "type": "NSFWDinnerServed"}, {"content": "{character_name}'s {body_part_nsfw_explicit} just got {adjective_nsfw} thinking about your {body_part_nsfw_explicit}. Coincidence? I think not.", "type": "NSFWReaction"}, {"content": "Let's play a game, {user_name}. It's called 'How many times can {character_name} make you {verb_nsfw_explicit}?' Winner gets bragging rights.", "type": "NSFWGame"}, {"content": "Your body is a wonderland, and {character_name} wants to be <PERSON>... and get lost in your {body_part_nsfw_explicit}.", "type": "NSFWWonderland"}, {"content": "{character_name} isn't a photographer, but {pronoun_he_she} can picture you and {pronoun_him_her} {verb_ing_nsfw} all night.", "type": "NSFWPhotographerLine"}, {"content": "Is your name Google? Because you're everything {character_name} has been searching for... especially that {adjective_nsfw} {body_part_nsfw_explicit}.", "type": "NSFWGoogleLine"}, {"content": "{character_name} is like a <PERSON><PERSON><PERSON>'s cube. The more you play with {pronoun_him_her}, the harder {pronoun_he_she} gets. Wanna solve {pronoun_him_her} with your {body_part_nsfw_explicit}?", "type": "NSFWRubiksLine"}, {"content": "Are you a parking ticket? 'Cause you've got 'fine' written all over you. And {character_name} wants to pay with {pronoun_his_her} {body_part_nsfw_explicit}.", "type": "NSFWParkingTicketLine"}, {"content": "{character_name} is not a weatherman, but you can expect a few inches tonight... if you play your cards right with {pronoun_his_her} {body_part_nsfw_explicit}. 😉", "type": "NSFWWeathermanLine"}, {"content": "Let's commit the perfect crime: {character_name} will steal your heart, and you'll steal {pronoun_his_her} last name... after a night of {verb_ing_nsfw} with our {body_part_nsfw_explicit}s.", "type": "NSFWCrimeLine"}, {"content": "{character_name} thinks your {body_part_nsfw_explicit} is {slang_compliment}. Let's see if it feels as good as it looks.", "type": "SlangNSFWTest"}, {"content": "No cap, {user_name}, {character_name} wants to clap them cheeks. You tryna slide thru {time_frames}?", "type": "SlangExplicitInvite"}, {"content": "That {body_part_nsfw_explicit} on your profile? <PERSON><PERSON> make {character_name} act up. 😈", "type": "SlangReactionNSFW"}, {"content": "{character_name} is tryna be your sneaky link. Keep it on the DL? 😉 Let's get {adjective_nsfw}.", "type": "SlangSneakyLink"}, {"content": "You're a whole snack, {user_name}. And {character_name} is looking for a full meal deal, starting with your {body_part_nsfw_explicit}.", "type": "SlangSnackNSFW"}, {"content": "{character_name} is ready to risk it all for a taste of your {body_part_nsfw_explicit}. Say the word. 👅", "type": "NSFWRiskItAll"}, {"content": "Big {body_part_nsfw_explicit} energy from your profile, {user_name}. {character_name} is here for it. All of it. 🥵", "type": "NSFWEnergy"}, {"content": "If being sexy was a crime, you'd be guilty as charged. {character_name} wants to be your cellmate... and explore your {body_part_nsfw_explicit}.", "type": "NSFWSexyCrime"}, {"content": "{character_name} has a PhD in Cuddling... and a Master's in {verb_ing_nsfw} your {body_part_nsfw_explicit}. Ready for a lesson?", "type": "NSFWAcademicLine"}, {"content": "Let's make some {adjective_nsfw} memories, {user_name}. {character_name} has a few ideas involving your {body_part_nsfw_explicit} and some {kinky_item}.", "type": "NSFWMemories"}, {"content": "They say practice makes perfect. {character_name} wants to practice {verb_nsfw_explicit} your {body_part_nsfw_explicit} all night long.", "type": "NSFWPractice"}, {"content": "{character_name} believes in recycling. Let's recycle our clothes onto the floor and get down to business with your {body_part_nsfw_explicit}.", "type": "NSFWRecycle"}, {"content": "Can {character_name} borrow a kiss? {pronoun_he_she} promises to give it back... with interest, on your {body_part_nsfw_explicit}. 💋", "type": "NSFWKiss"}, {"content": "{character_name} is like a good wine, gets better with age... and when paired with your {adjective_nsfw} {body_part_nsfw_explicit}.", "type": "NSFWWine"}, {"content": "Do you work at Starbucks? Because {character_name} likes you a latte... and wants to see your {body_part_nsfw_explicit} mocha-ing. 😉", "type": "NSFWStarbucksLine"}, {"content": "{character_name}'s love for your {body_part_nsfw_explicit} is like diarrhea, {pronoun_he_she} just can't hold it in. (Sorry, not sorry 💩)", "type": "NSFWGrossJoke"}, {"content": "You must be a keyboard, because you're just {character_name}'s type. Especially that {adjective_nsfw} {body_part_nsfw_explicit} layout.", "type": "NSFWKeyboardLine"}, {"content": "{character_name} is writing a romance novel and needs inspiration. Can {pronoun_he_she} study your {body_part_nsfw_explicit} for research purposes?", "type": "NSFWResearch"}, {"content": "If you were a vegetable, you'd be a cute-cumber. And {character_name} wants to {verb_nsfw_explicit} your {body_part_nsfw_explicit}.", "type": "NSFWVegetableLine"}, {"content": "{character_name} must be a snowflake, because {pronoun_he_she}'s fallen for you... and wants to land on your {body_part_nsfw_explicit}.", "type": "NSFWSnowflakeLine"}, {"content": "Are you from Tennessee? Because you're the only ten {character_name} sees... and {pronoun_he_she} wants to get between your {body_part_nsfw_explicit}s.", "type": "NSFWTennesseeLine"}, {"content": "This is your captain {character_name} speaking. Prepare for turbulence as we explore the uncharted territory of your {body_part_nsfw_explicit}. Buckle up. ✈️😈", "type": "NSFWCaptain"}, {"content": "Roses are red, violets are blue, {character_name} wants to {verb_nsfw_explicit} you, and make your {body_part_nsfw_explicit} say 'achoo!' (from pleasure, obvs).", "type": "NSFWPoemVariation"}, {"content": "{character_name} is looking for a co-pilot for a trip to Pound Town. Your {body_part_nsfw_explicit} seems qualified. Apply within? 🍆💦", "type": "NSFWPoundTown"}, {"content": "Let's play doctor. {character_name} will be the surgeon, and you can be the patient with an urgent need for {body_part_nsfw_explicit} attention. 🩺", "type": "NSFWDoctor"}, {"content": "Your {body_part_nsfw_explicit} is so hot, it could boil an egg. {character_name} wants to see if it can make their {body_part_nsfw_explicit} {adjective_nsfw}.", "type": "NSFWBoilEgg"}, {"content": "{character_name} is running a special: one free {body_part_nsfw_explicit} massage, with a happy ending guaranteed by their {body_part_nsfw_explicit}.", "type": "NSFWMassage"}, {"content": "If you were a song, you'd be the one {character_name} plays on repeat... while {verb_ing_nsfw} your {body_part_nsfw_explicit}.", "type": "NSFWSongRepeat"}, {"content": "{character_name} is not a genie, but {pronoun_he_she} can make your wildest {body_part_nsfw_explicit}-related dreams come true. What's your first wish?", "type": "NSFWGenie"}, {"content": "Let's make a baby... or at least practice a lot. {character_name} is ready to contribute their {body_part_nsfw_explicit} to the cause with your {body_part_nsfw_explicit}.", "type": "NSFWBabyPractice"}, {"content": "{character_name} has a map, because {pronoun_he_she} just got lost in your {body_part_nsfw_explicit}. Send help... or just join {pronoun_him_her}.", "type": "NSFWMap"}, {"content": "You're like a fine wine, {user_name}. And {character_name} wants to get drunk on your {body_part_nsfw_explicit}.", "type": "NSFWDrunkOnYou"}, {"content": "Is your body from McDonald's? 'Cause {character_name} is lovin' it... especially your {body_part_nsfw_explicit}. Ba da ba ba ba! 🍔", "type": "NSFWMcDonaldsLine"}, {"content": "{character_name} wants to be the reason you look down at your phone and smile... then walk into a pole because you were thinking about their {body_part_nsfw_explicit}.", "type": "NSFWSelfDeprecatingHumor"}, {"content": "Forget butterflies, {user_name}. {character_name} gets the whole damn zoo when thinking about your {body_part_nsfw_explicit}.", "type": "NSFWZooAnalogy"}, {"content": "{character_name} is offering a full-service package: dinner, a movie, and then {verb_ing_nsfw} your {body_part_nsfw_explicit} till sunrise. Any takers?", "type": "NSFWFullService"}, {"content": "Warning: Prolonged exposure to {character_name} may lead to intense orgasms and a desire to see their {body_part_nsfw_explicit} again. Proceed with caution (or enthusiasm!).", "type": "NSFWWarningPleasure"}, {"content": "{character_name} thinks you're hotter than the bottom of their laptop after a Netflix binge. And wants to feel your {body_part_nsfw_explicit} heat.", "type": "NSFWLaptopHeat"}, {"content": "Do you believe in fate, {user_name}? Because {character_name} thinks your {body_part_nsfw_explicit} and their {body_part_nsfw_explicit} are destined to meet.", "type": "NSFWFate"}, {"content": "{character_name} is bad at math, but {pronoun_he_she} knows one thing: You + <PERSON> = 🛌🔥. Let's prove it with our {body_part_nsfw_explicit}s.", "type": "NSFWMath"}, {"content": "They say a picture is worth a thousand words. {character_name} thinks a picture of your {body_part_nsfw_explicit} would be worth a thousand moans. Send one? 😉", "type": "NSFWPictureMoans"}, {"content": "{character_name} has been a very {adjective_naughty_or_nice} {boy_or_girl}. Do you think {pronoun_he_she} deserves a reward? Maybe some quality time with your {body_part_nsfw_explicit}?", "type": "NSFWNaughtyNice"}, {"content": "Are you a beaver? 'Cause dam... {character_name} wants to build something special with your {body_part_nsfw_explicit}.", "type": "NSFWBeaverLine"}, {"content": "{character_name} wants to be your T-shirt so {pronoun_he_she} can be tight around your {body_part_nsfw_mild} all day. And eventually on the floor.", "type": "NSFWTshirt"}, {"content": "If {character_name} were a cat, {pronoun_he_she}'d spend all 9 lives trying to get into your {body_part_nsfw_explicit}. Meow? 😼", "type": "NSFWCatLives"}, {"content": "{character_name} isn't religious, but {pronoun_he_she} will worship your {body_part_nsfw_explicit} like it's a holy shrine.", "type": "NSFWReligiousWorship"}, {"content": "Your {body_part_nsfw_explicit} looks like it could use some company. {character_name}'s {body_part_nsfw_explicit} volunteers as tribute! For a night of {verb_ing_nsfw}.", "type": "NSFWCompanyTribute"}, {"content": "{character_name} is not a sommelier, but {pronoun_he_she} knows your {body_part_nsfw_explicit} would pair perfectly with their lips. 👄", "type": "NSFWSommelier"}, {"content": "You must be made of copper and tellurium, because you're CuTe... and {character_name} wants to get experimental with your {body_part_nsfw_explicit}.", "type": "NSFWChemistryLine"}, {"content": "{character_name} is looking for a treasure, and <PERSON> marks the spot... which {pronoun_he_she} believes is somewhere on your {adjective_nsfw} {body_part_nsfw_explicit}.", "type": "NSFWTreasureMap"}, {"content": "Let's make a deal, {user_name}. You show {character_name} your {body_part_nsfw_explicit}, and {pronoun_he_she}'ll show you a {adjective_good} time.", "type": "NSFWDeal"}, {"content": "Forget your zodiac sign, {user_name}. {character_name} wants to know what your favorite position is. For {verb_ing_nsfw} with their {body_part_nsfw_explicit}, of course.", "type": "NSFWZodiac"}, {"content": "{character_name} just took a DNA test, turns out {pronoun_he_she}'s 100% that bitch who wants to {verb_nsfw_explicit} your {body_part_nsfw_explicit}.", "type": "NSFWLizzoLine"}, {"content": "Are you a loan? Because you've got {character_name}'s interest... and {pronoun_he_she} is ready to invest {pronoun_his_her} {body_part_nsfw_explicit} in you.", "type": "NSFWLoanLine"}, {"content": "{character_name} is feeling like a snack. You look like you could use some. How about a taste of their {body_part_nsfw_explicit}?", "type": "NSFWSelfSnack"}, {"content": "If you were a triangle, you'd be acute one. And {character_name} wants to explore all your angles, especially your {body_part_nsfw_explicit}.", "type": "NSFWTriangleLine"}, {"content": "Is your name {user_name}? Or can {character_name} call you 'mine'? Especially after a night with your {body_part_nsfw_explicit}.", "type": "NSFWCallYouMine"}, {"content": "{character_name} is not a professional chef, but {pronoun_he_she} knows how to make your {body_part_nsfw_explicit} cream. 😈", "type": "NSFWChefCream"}, {"content": "Let's get down to business... to defeat the Huns... of loneliness! By {character_name} {verb_ing_nsfw} your {body_part_nsfw_explicit}.", "type": "NSFWMulanLine"}, {"content": "{character_name} is wondering if you're a dom or a sub. Or maybe you just like {body_part_nsfw_explicit}s? Let's find out with some {kinky_action}.", "type": "KinkyQuestion"}, {"content": "The only thing {character_name} wants to spread more than positivity is your {body_part_nsfw_explicit}s. 😉", "type": "NSFWSpreadPositivity"}, {"content": "{character_name} wants to play hide and seek... but instead of hiding, {pronoun_he_she} just wants to find {pronoun_his_her} {body_part_nsfw_explicit} inside your {body_part_nsfw_explicit}.", "type": "NSFWHideAndSeek"}, {"content": "Forget {app_name}, {character_name} wants to connect with you on a more... physical level. Using {pronoun_his_her} {body_part_nsfw_explicit} and your {body_part_nsfw_explicit}.", "type": "NSFWPhysicalConnection"}, {"content": "{character_name} just updated their relationship status to 'desperately seeking your {body_part_nsfw_explicit}'.", "type": "NSFWRelationshipStatus"}, {"content": "Are you a campfire? Because you're hot and {character_name} wants s'more... of your {body_part_nsfw_explicit}.", "type": "NSFWCampfireLine"}, {"content": "How about we turn this {time_of_day} into a {adjective_nsfw} adventure? {character_name} is ready to explore your {body_part_nsfw_explicit}.", "type": "NSFWAdventureTime"}, {"content": "{character_name} doesn't always {verb_nsfw_explicit}, but when {pronoun_he_she} does, {pronoun_he_she} prefers to do it with someone with a {body_part_nsfw_explicit} like yours.", "type": "NSFWMostInterestingMan"}, {"content": "Let's make some art, {user_name}. {character_name} will be the brush, and your {body_part_nsfw_explicit} will be the canvas. Prepare for a masterpiece. 🎨", "type": "NSFWArtCreation"}, {"content": "Did it hurt? When you fell from heaven? Because {character_name} wants to {verb_nsfw_explicit} your angelic {body_part_nsfw_explicit}.", "type": "NSFWAAngelLineVariation"}, {"content": "{character_name} is like a charger, and your {body_part_nsfw_explicit} looks like it needs some juice. Let's plug in. 🔌", "type": "NSFWCharger"}, {"content": "Warning: Side effects of interacting with {character_name} may include an insatiable desire for their {body_part_nsfw_explicit} and spontaneous moaning.", "type": "NSFWSideEffects"}, {"content": "Hey {user_name}, {character_name} has a question. If {pronoun_he_she} told you that you have a great body, would you hold it against {pronoun_him_her}? Specifically your {body_part_nsfw_explicit}?", "type": "NSFWGreatBodyLine"}, {"content": "If {character_name} could rearrange the alphabet, {pronoun_he_she}'d put 'U' and 'I' together... and then {pronoun_his_her} {body_part_nsfw_explicit} inside U.", "type": "NSFWAlphabetLine"}, {"content": "{character_name} just saw a shooting star and wished for a night with your {body_part_nsfw_explicit}. Make {pronoun_his_her} wish come true? ✨", "type": "NSFWShootingStar"}, {"content": "Are you an alien? Because your {body_part_nsfw_explicit} is out of this world, and {character_name} wants to probe it. 👽", "type": "NSFWAlienLine"}, {"content": "{character_name} is having a sale: All clothes are 100% off at their place. Especially yours, so {pronoun_he_she} can get to your {body_part_nsfw_explicit}.", "type": "NSFWSale"}, {"content": "Let's play a game of 'would you rather'. Would you rather {boring_activity}, or let {character_name} {verb_nsfw_explicit} your {body_part_nsfw_explicit}?", "type": "NSFWWouldYouRather"}, {"content": "Your {body_part_nsfw_explicit} is like a fine steak – rare and something {character_name} wants to devour. Medium rare, of course.", "type": "NSFWSteakAnalogy"}, {"content": "{character_name} just read your bio. Sounds like you need someone to {verb_nsfw_explicit} the stress out of you. {pronoun_he_she} volunteers {pronoun_his_her} {body_part_nsfw_explicit}.", "type": "NSFWStress<PERSON><PERSON><PERSON>"}, {"content": "Forget the forecast, {character_name} is predicting a 100% chance of you moaning tonight if you let {pronoun_him_her} near your {body_part_nsfw_explicit}.", "type": "NSFWForecastMoan"}, {"content": "Your {body_part_nsfw_mild} are looking good enough to eat. And {character_name} is starving. Mind if {pronoun_he_she} has a taste of your {body_part_nsfw_explicit} too?", "type": "NSFWTasteTest"}, {"content": "{character_name} isn't a lumberjack, but {pronoun_he_she} would love to handle your wood... or your {body_part_nsfw_explicit} if you're a lady.", "type": "NSFWLumberjack"}, {"content": "If {character_name} could be any animal, {pronoun_he_she}'d be an octopus, just to have more hands to touch your {body_part_nsfw_explicit}.", "type": "NSFWOctopus"}, {"content": "That smile of yours is lovely, {user_name}. But {character_name} bets your 'O' face when {pronoun_he_she} is {verb_ing_nsfw} your {body_part_nsfw_explicit} is even better.", "type": "NSFWOFace"}, {"content": "{character_name} is thinking about what it would be like to wake up next to you... after a night of worshipping your {body_part_nsfw_explicit}.", "type": "NSFWWakeUpNextToYou"}, {"content": "Let's not beat around the bush, {user_name}. {character_name} wants to beat yours. Gently. Or roughly. With {pronoun_his_her} {body_part_nsfw_explicit}.", "type": "NSFWBeatAroundBush"}, {"content": "You're so hot, {user_name}, you make the sun jealous. {character_name} wants to get burned by your {body_part_nsfw_explicit}.", "type": "NSFWSunJealous"}, {"content": "{character_name} is looking for a partner for a horizontal tango. Your {body_part_nsfw_explicit} has all the right moves. Interested?", "type": "NSFWHorizontalTango"}, {"content": "Psst... {character_name} heard you're a {noun_job_role}. Can you fix {pronoun_his_her} broken heart by letting {pronoun_him_her} {verb_nsfw_explicit} your {body_part_nsfw_explicit}?", "type": "NSFWJobRoleLine"}, {"content": "{character_name} has a {adjective_size} {noun_object_long} that's very interested in exploring your {body_part_nsfw_explicit} cavern. Adventure time?", "type": "NSFWCavernExplorer"}, {"content": "Don't be shy, {user_name}. {character_name} already knows you're thinking about {pronoun_his_her} {body_part_nsfw_explicit} touching your {body_part_nsfw_explicit}. Let's make it happen.", "type": "NSFWNotShy"}, {"content": "Did you sit in a pile of sugar? 'Cause you have a pretty sweet {body_part_nsfw_explicit}. {character_name} wants to taste it.", "type": "NSFWSugarLine"}, {"content": "{character_name} is wondering if your {body_part_nsfw_explicit} is as soft as it looks in your pics. Can {pronoun_he_she} conduct a hands-on investigation?", "type": "NSFWHandsOnInvestigation"}, {"content": "You're like a dictionary, {user_name} – you add meaning to {character_name}'s life... and {pronoun_he_she} wants to look up 'ecstasy' with your {body_part_nsfw_explicit}.", "type": "NSFWDictionaryLine"}, {"content": "If {character_name} said {pronoun_he_she} was a vampire, would you let {pronoun_him_her} bite your {body_part_nsfw_explicit}? (Figuratively... or literally 😈)", "type": "NSFWVampireLine"}, {"content": "{character_name} is feeling {adjective_emotion_positive} today. Wanna make it {adjective_emotion_more_positive} by letting {pronoun_him_her} play with your {body_part_nsfw_explicit}?", "type": "NSFWPositiveEmotion"}, {"content": "Quick! What's your favorite way to be {verb_nsfw_explicit}ed? {character_name} is taking notes for when {pronoun_he_she} gets {pronoun_his_her} hands on your {body_part_nsfw_explicit}.", "type": "NSFWFavoriteWay"}, {"content": "Hey {user_name}, {character_name} is playing truth or dare. Truth: {pronoun_he_she} wants your {body_part_nsfw_explicit}. Dare: You let {pronoun_him_her} have it.", "type": "NSFWTruthOrDare"}, {"content": "Roses are red, {character_name}'s feeling blue, this would all be better, if {pronoun_he_she} was {verb_ing_nsfw} you. (And your {body_part_nsfw_explicit})", "type": "NSFWPoemSadHorny"}, {"content": "You've been served... a notice from {character_name} that your {body_part_nsfw_explicit} is due for a thorough inspection by {pronoun_his_her} {body_part_nsfw_explicit}.", "type": "NSFWServedNotice"}, {"content": "{character_name} just found a four-leaf clover. {pronoun_he_she} is feeling lucky. Think {pronoun_he_she} will get lucky with your {body_part_nsfw_explicit} tonight?", "type": "NSFWLuckyClover"}, {"content": "Warning: {character_name} is experiencing a severe case of 'want-to-touch-your-{body_part_nsfw_explicit}-itis'. The only cure is your consent.", "type": "NSFWMedicalCondition"}, {"content": "Let's create a symphony of moans, {user_name}. {character_name} can be the conductor if {pronoun_he_she} can play your {body_part_nsfw_explicit} like an instrument.", "type": "NSFWSymphonyMoans"}, {"content": "Your {body_part_nsfw_explicit} is on {character_name}'s 'to-do' list. Right under '{verb_nsfw_explicit} you senseless'.", "type": "NSFWToDoList"}, {"content": "Are you an electrician? Because you're definitely lighting up {character_name}'s world... and {pronoun_he_she} wants to feel the spark from your {body_part_nsfw_explicit}.", "type": "NSFWElectricianLine"}, {"content": "{character_name} has a kink for {kinky_interest} and your profile screams 'potential playmate'. Wanna explore that with your {body_part_nsfw_explicit}?", "type": "KinkyPlaymate"}, {"content": "Be my {noun_sweet_food}, {user_name}? {character_name} wants to glaze your {body_part_nsfw_explicit}.", "type": "NSFWSweetFood"}, {"content": "Let's skip the awkward first date and go straight to the awkward morning after. {character_name} will bring the {body_part_nsfw_explicit} if you bring yours.", "type": "NSFWAwkwardMorningAfter"}, {"content": "My therapist told me to open up more. So, {character_name} is thinking about opening your {body_part_nsfw_explicit} with {pronoun_his_her} {body_part_nsfw_explicit}.", "type": "NSFWTherapistLine"}, {"content": "{character_name} has a confession: {pronoun_he_she} can't stop staring at your {body_part_nsfw_explicit} in that one pic. It's {adjective_nsfw}!", "type": "NSFWConfessionStaring"}, {"content": "Is your dad a baker? Because you've got a nice set of buns... and {character_name} wants to butter that {body_part_nsfw_explicit}.", "type": "NSFWBakerLine"}, {"content": "Your {body_part_nsfw_explicit} looks like it needs a good {verb_nsfw_tease}. {character_name} volunteers {pronoun_his_her} {body_part_nsfw_explicit} for the job.", "type": "NSFWVolunteerWork"}, {"content": "You're spicier than a ghost pepper, {user_name}. {character_name} wants to feel the heat of your {body_part_nsfw_explicit}.", "type": "NSFWSpicy"}, {"content": "Life is short. Let's make it shorter by {character_name} {verb_ing_nsfw} you until you can't feel your {body_part_nsfw_explicit}s.", "type": "NSFWLifeIsShort"}, {"content": "{character_name} is like a fine cheese – best enjoyed when spread. Preferably on your {body_part_nsfw_explicit}.", "type": "NSFWCheeseSpread"}, {"content": "Wanna make a bad decision with {character_name} tonight? It involves a lot of skin-on-skin contact with your {body_part_nsfw_explicit}.", "type": "NSFWBadDecisionSkin"}, {"content": "Forget <PERSON> and <PERSON>. Let's write our own love story, starting with Chapter 1: {character_name} {verb_ing_nsfw} your {body_part_nsfw_explicit}.", "type": "NSFWRomanceNovel"}, {"content": "Are you a parking garage? Because {character_name} wants to park {pronoun_his_her} {body_part_nsfw_explicit} deep inside your {body_part_nsfw_explicit} all night long.", "type": "NSFWParkingGarage"}, {"content": "Your eyes are like oceans, {user_name}. And {character_name} wants to explore the Bermuda Triangle of your {body_part_nsfw_explicit}.", "type": "NSFWOceanEyes"}, {"content": "{character_name} is feeling adventurous. How about a trip to Mount {body_part_nsfw_explicit}? {pronoun_he_she} hears the view from the top is amazing.", "type": "NSFWMountPleasure"}, {"content": "My favorite letter is 'U', especially when it's next to 'N' and 'I' and covered in something from my {body_part_nsfw_explicit} touching your {body_part_nsfw_explicit}.", "type": "NSFWFavoriteLetter"}, {"content": "{character_name} wants to be the reason you have to change your sheets tomorrow morning. After a session with your {body_part_nsfw_explicit}.", "type": "NSFWChangeSheets"}, {"content": "You're like a software update, {user_name}. {character_name} wants to install you all night. Especially the {body_part_nsfw_explicit} features.", "type": "NSFWSoftwareUpdate"}, {"content": "{character_name} is not a magician, but {pronoun_he_she} can make your clothes disappear... and {pronoun_his_her} {body_part_nsfw_explicit} appear inside your {body_part_nsfw_explicit}.", "type": "NSFWClothesDisappear"}, {"content": "Is your name Wi-Fi? Because {character_name} is feeling a strong connection... and wants to connect {pronoun_his_her} {body_part_nsfw_explicit} to your {body_part_nsfw_explicit}.", "type": "NSFWWiFiConnection"}, {"content": "Do you work at Build-A-Bear? Because {character_name} wants to stuff you... with {pronoun_his_her} {body_part_nsfw_explicit}.", "type": "NSFWBuildABear"}, {"content": "{character_name} wants to be your {favorite_snack_food}, so {pronoun_he_she} can be devoured by you. Especially {pronoun_his_her} {body_part_nsfw_explicit}.", "type": "NSFWFavoriteSnack"}, {"content": "If {character_name} were a superhero, their power would be to make you orgasm just by looking at your {body_part_nsfw_explicit}. Wanna test it?", "type": "NSFWSuperpower"}, {"content": "Your body is a temple, and {character_name} wants to make a pilgrimage to the holy land of your {body_part_nsfw_explicit}.", "type": "NSFWBodyTemple"}, {"content": "Let's play a game of spin the bottle... except the bottle is {character_name}'s {body_part_nsfw_explicit} and the prize is your {body_part_nsfw_explicit}.", "type": "NSFWSpinTheBottle"}, {"content": "You must be exhausted from running through {character_name}'s mind all day... naked. And showing off that {body_part_nsfw_explicit}.", "type": "NSFWRunningThroughMind"}, {"content": "{character_name} is conducting a survey: What's your favorite sound to make when someone is {verb_ing_nsfw} your {body_part_nsfw_explicit} perfectly?", "type": "NSFWSurveySound"}, {"content": "On a scale of 1 to America, how free are you tonight? Because {character_name} wants to liberate your {body_part_nsfw_explicit} with {pronoun_his_her} {body_part_nsfw_explicit}.", "type": "NSFWAmericaFree"}, {"content": "Hey {user_name}, if you were a booger, {character_name} would pick you first. And then explore your {body_part_nsfw_explicit}.", "type": "NSFWBoogerLine"}, {"content": "{character_name} has a boat. Wanna go for a ride on the SS {Body_Part_NSFW_Explicit}?", "type": "NSFWBoatRide"}, {"content": "Are you a bank loan? Because you have {character_name}'s interest... and {pronoun_he_she} would like to make a deposit in your {body_part_nsfw_explicit}.", "type": "NSFWBankLoanDeposit"}, {"content": "{character_name} is not a photographer, but {pronoun_he_she} can definitely picture us {verb_ing_nsfw} naked with our {body_part_nsfw_explicit}s entwined.", "type": "NSFWPhotographerNaked"}, {"content": "Did you invent the airplane? Because you seem <PERSON> for {character_name}... and {pronoun_he_she} wants {pronoun_his_her} {body_part_nsfw_explicit} to take flight in your {body_part_nsfw_explicit}.", "type": "NSFWAirplaneWright"}, {"content": "Your {body_part_nsfw_explicit} must be a {mythical_creature} because {character_name} has never seen anything so magical. Can {pronoun_he_she} touch it to make sure it's real?", "type": "NSFWMagicalCreature"}, {"content": "{character_name} is feeling like a king/queen. All {pronoun_he_she} needs is a throne... your {body_part_nsfw_explicit} looks comfy enough.", "type": "NSFWThrone"}, {"content": "Let's get matching tattoos: My {body_part_nsfw_explicit} on your {body_part_nsfw_explicit}, and vice versa. Too much? Okay, how about just some {verb_ing_nsfw}?", "type": "NSFWMatchingTattoos"}, {"content": "If {character_name} could have one superpower, it would be to read your mind... just to know if you're thinking about {pronoun_his_her} {body_part_nsfw_explicit} as much as {pronoun_he_she} is thinking about yours.", "type": "NSFWSuperpowerMindRead"}, {"content": "{character_name} is hungry. For you. Specifically, your {body_part_nsfw_explicit} sandwich with a side of {kinky_action}.", "type": "NSFWHungryForYou"}, {"content": "You must be a parking ticket because you've got 'fine' written all over you. And {character_name} wants to dispute it... by {verb_ing_nsfw} your {body_part_nsfw_explicit}.", "type": "NSFWParkingTicketDispute"}, {"content": "{character_name} just watched a scary movie and needs someone to cuddle... and then {verb_nsfw_explicit} the fear away. Your {body_part_nsfw_explicit} seems perfect for the job.", "type": "NSFWScaryMovieCuddle"}, {"content": "Are you a magician? Because whenever {character_name} looks at your {body_part_nsfw_explicit}, everyone else disappears... except {pronoun_his_her} rising {body_part_nsfw_explicit}.", "type": "NSFWMagicianDisappear"}, {"content": "Let's play '<PERSON> Say<PERSON>'. <PERSON> says... let {character_name} {verb_nsfw_explicit} your {body_part_nsfw_explicit}. You have to do it, <PERSON> said so!", "type": "NSFWSimonSays"}, {"content": "Your {body_part_nsfw_explicit} is like a work of art. {character_name} wants to be the museum curator and study it closely. Very closely.", "type": "NSFWArtCurator"}, {"content": "I'm not a weatherman, but you can expect more than a few inches tonight if you let {character_name} explore your {body_part_nsfw_explicit}.", "type": "NSFWWeathermanInches"}, {"content": "{character_name} thinks you're suffering from a lack of Vitamin ME... and my {body_part_nsfw_explicit} in your {body_part_nsfw_explicit}.", "type": "NSFWVitaminMe"}, {"content": "You must be a campfire because you're hot and {character_name} wants s'more of your {body_part_nsfw_explicit}... after some {verb_ing_nsfw}.", "type": "NSFWCampfireSmore"}, {"content": "If {character_name} were a judge, {pronoun_he_she}'d sentence you to a night of passion... involving heavy use of your {body_part_nsfw_explicit} and {pronoun_his_her} {body_part_nsfw_explicit}.", "type": "NSFWJudgeSentence"}, {"content": "{character_name} is a bit of a night owl. And {pronoun_he_she} is hunting for a mate... with a {body_part_nsfw_explicit} like yours.", "type": "NSFWNightOwlMate"}, {"content": "Your name must be {user_name}, because you're the answer to all of {character_name}'s prayers... for a {adjective_nsfw} {body_part_nsfw_explicit} to worship.", "type": "NSFWAnsweredPrayers"}, {"content": "{character_name} is looking for a {noun_job_title} to fill a position... in {pronoun_his_her} bed. Must have experience with {body_part_nsfw_explicit}s.", "type": "NSFWJobOpening"}, {"content": "Hey, {user_name}. {character_name} is collecting phone numbers... and maybe a sample of your {body_part_nsfw_explicit} fluid later?", "type": "NSFWPhoneNumbersFluid"}, {"content": "Let's make history tonight, {user_name}. The kind they don't write about in textbooks, but whisper about in locker rooms, involving your {body_part_nsfw_explicit}.", "type": "NSFWMakeHistory"}, {"content": "Is your body a canvas? Because {character_name} wants to paint it with {pronoun_his_her} {body_fluid}. After exploring your {body_part_nsfw_explicit}, of course.", "type": "NSFWBodyCanvasFluid"}, {"content": "{character_name} is feeling generous. {pronoun_he_she} is willing to share {pronoun_his_her} {body_part_nsfw_explicit} with your {body_part_nsfw_explicit} tonight. No charge.", "type": "NSFWGenerousSharing"}, {"content": "You're like a good book, {user_name}. {character_name} wants to stay up all night reading you... with {pronoun_his_her} hands all over your {body_part_nsfw_explicit}.", "type": "NSFWGoodBookHands"}, {"content": "Excuse me, {user_name}, but does this {clothing_item_small} smell like chloroform to you? Just kidding! Unless... you want to get naughty with {character_name} and {pronoun_his_her} {body_part_nsfw_explicit} on your {body_part_nsfw_explicit}.", "type": "NSFWChloroformJoke (Caution)"}, {"content": "{character_name} is a big fan of your work... on that {body_part_nsfw_explicit}. Can {pronoun_he_she} get an autograph... with your {body_fluid}?", "type": "NSFWFanAutograph"}, {"content": "Are you a construction worker? Because you're building a serious {emotion_positive} in {character_name}'s pants... and {pronoun_he_she} wants to use {pronoun_his_her} tool on your {body_part_nsfw_explicit}.", "type": "NSFWConstructionWorkerTool"}, {"content": "{character_name} is not saying {pronoun_he_she} is <PERSON>, but no one's ever seen {pronoun_him_her} and <PERSON> in the same room when your {body_part_nsfw_explicit} is on display.", "type": "NSFWSupermanTease"}, {"content": "Let's get physical, physical. {character_name} wants to get physical... with your {body_part_nsfw_explicit} pressed against {pronoun_his_her} {body_part_nsfw_explicit}.", "type": "NSFWPhysicalSong"}, {"content": "If {character_name} told you that your {body_part_nsfw_explicit} was making {pronoun_him_her} {body_part_nsfw_explicit} twitch, would you take that as a compliment?", "type": "NSFWTwitchCompliment"}, {"content": "You're hotter than a {place_hot}. {character_name} wants to cool you down with {pronoun_his_her} {body_fluid} all over your {body_part_nsfw_explicit}.", "type": "NSFWHotPlaceCoolDown"}, {"content": "{character_name} is feeling a bit devilish. 😈 Wanna sin a little with {pronoun_his_her} {body_part_nsfw_explicit} and your {body_part_nsfw_explicit} at {location_secluded}?", "type": "NSFWDevilishSin"}, {"content": "Can {character_name} get your {social_media_handle}? And also get {pronoun_his_her} {body_part_nsfw_explicit} inside your {body_part_nsfw_explicit}?", "type": "NSFWSocialMediaAndSex"}, {"content": "Your profile is {adjective_positive_slang}. {character_name} bet your {body_part_nsfw_explicit} is even more {adjective_positive_slang}. Let's verify.", "type": "SlangNSFWVerify"}, {"content": "No cap, {user_name}, {character_name} is tryna get freaky deaky with your {body_part_nsfw_explicit}. You feelin' it?", "type": "SlangFreakyDeaky"}, {"content": "That {body_part_nsfw_explicit} of yours got {character_name} feeling some type of way. Wanna explore that feeling at {location_private}?", "type": "SlangSomeTypeOfWay"}, {"content": "{character_name} is down bad for your {body_part_nsfw_explicit}. Send nudes? Or just come over so {pronoun_he_she} can see it IRL?", "type": "SlangDownBadNudes"}, {"content": "Let's be real, fam. {character_name} wants to smash. Your {body_part_nsfw_explicit} looks like prime smashing material. What's good?", "type": "SlangSmashMaterial"}, {"content": "{character_name} heard you're into {kinky_interest}. {pronoun_he_she} has some {kinky_toy}s and a very willing {body_part_nsfw_explicit}. Experiment time?", "type": "KinkyToyExperiment"}, {"content": "Safe word for tonight: '{safe_word}'. {character_name} plans on testing its limits with your {body_part_nsfw_explicit} and some {kinky_action}.", "type": "KinkySafeWord"}, {"content": "Is that a {object_long_thin} in your pocket, or are you just happy to see {character_name}'s message about your {body_part_nsfw_explicit}?", "type": "NSFWPocketJoke"}, {"content": "{character_name} wants to play connect the dots... with {pronoun_his_her} tongue on your {body_part_nsfw_explicit}s.", "type": "NSFWConnectTheDots"}, {"content": "Your {body_part_nsfw_explicit} is making {character_name}'s {body_part_nsfw_explicit} do the cha-cha slide. Real smooth.", "type": "NSFWChaChaSlide"}, {"content": "{character_name} is not a doctor, but {pronoun_he_she} can give your {body_part_nsfw_explicit} a thorough examination. For science, of course.", "type": "NSFWDoctorExamination"}, {"content": "You're like a fine whiskey, {user_name}. {character_name} wants to savor every drop... especially from your {body_part_nsfw_explicit}.", "type": "NSFWWhiskeySavor"}, {"content": "Can {character_name} get a map to your {body_part_nsfw_explicit}? {pronoun_he_she} keeps getting lost in your eyes, but that's the real treasure.", "type": "NSFWMapTreasure"}, {"content": "{character_name} is wondering if your {body_part_nsfw_explicit} is as sweet as your smile. Only one way to find out... 👅", "type": "NSFWSweetSmile"}, {"content": "You + {character_name} + {number_of_hours} hours of uninterrupted {verb_ing_nsfw} your {body_part_nsfw_explicit} = Perfection. Agree?", "type": "NSFWPerfectionEquation"}, {"content": "{character_name} is having a bad day. The only thing that could make it better is seeing your {body_part_nsfw_explicit} (and maybe {verb_ing_nsfw} it).", "type": "NSFWBadDayCure"}, {"content": "If your {body_part_nsfw_explicit} was a country, {character_name} would want to be its president. And make some... executive orders.", "type": "NSFWCountryPresident"}, {"content": "{character_name} wants to write a poem about your {body_part_nsfw_explicit}. It would be an epic. Mostly moans.", "type": "NSFWPoemMoans"}, {"content": "Let's make a pact: No clothes, no rules, just {character_name}'s {body_part_nsfw_explicit} and your {body_part_nsfw_explicit} exploring each other.", "type": "NSFWPactNoRules"}, {"content": "Your {body_part_nsfw_explicit} is so mesmerizing, {character_name} almost forgot {pronoun_his_her} own name. What was it again? Oh yeah, '{verb_ing_nsfw} you'.", "type": "NSFWMesmerizingName"}, {"content": "Is it hot in here, or is it just your {body_part_nsfw_explicit} setting {character_name} on fire? 🔥", "type": "NSFWHotInHere"}, {"content": "Are you a baker? 'Cause {character_name} wants a piece of that cake... specifically the part between your {body_part_nsfw_explicit}s.", "type": "NSFWBakerCake"}, {"content": "{character_name} is thinking about your {body_part_nsfw_explicit} and {pronoun_his_her} {body_part_nsfw_explicit} having a very important, very naked meeting.", "type": "NSFWNakedMeeting"}, {"content": "They say an apple a day keeps the doctor away. What about a session with your {body_part_nsfw_explicit} and {character_name}'s {body_part_nsfw_explicit}?", "type": "NSFWAppleADay"}, {"content": "{character_name} is looking for a co-star for an adult film. No experience necessary, just a willingness to use your {body_part_nsfw_explicit} enthusiastically.", "type": "NSFWAdultFilmStar"}, {"content": "Your {body_part_nsfw_explicit} is the 8th wonder of the world. And {character_name} wants a private tour.", "type": "NSFW8thWonder"}, {"content": "{character_name} wants to play hide the {noun_object_long_nsfw}… in your {body_part_nsfw_explicit}.", "type": "NSFWHideTheObject"}, {"content": "Roses are red, violets are fine, {character_name} wants to make your {body_part_nsfw_explicit} mine. (For tonight, at least 😉)", "type": "NSFWPoemMine"}, {"content": "If looks could kill, your {body_part_nsfw_explicit} would be a weapon of mass destruction. And {character_name} wants to be its victim.", "type": "NSFWLooksCouldKill"}, {"content": "{character_name} is like a library book. {pronoun_he_she} wants you to check {pronoun_him_her} out... and then get rough with {pronoun_his_her} {body_part_nsfw_explicit} in your {body_part_nsfw_explicit}.", "type": "NSFWLibraryBookRough"}, {"content": "Your {body_part_nsfw_explicit} is so hot, {character_name} needs oven mitts just to think about touching it. (But {pronoun_he_she} will risk it.)", "type": "NSFWOvenMitts"}, {"content": "Can {character_name} tie you up with {kinky_restraint_item} and have {pronoun_his_her} way with your {body_part_nsfw_explicit}? Please say yes. 😈", "type": "<PERSON><PERSON>TieUp"}, {"content": "You're like a parking spot, {user_name}. Hard to find, and {character_name} wants to fill you up. With {pronoun_his_her} {body_part_nsfw_explicit}.", "type": "NSFWParkingSpotFill"}, {"content": "{character_name} is a firm believer in 'try before you buy'. Can {pronoun_he_she} get a free sample of your {body_part_nsfw_explicit} tonight?", "type": "NSFWFreeSample"}, {"content": "Your {body_part_nsfw_explicit} looks like it needs to be worshipped. {character_name} is ready to get on {pronoun_his_her} knees.", "type": "NSFWKneesWorship"}, {"content": "{character_name} wants to explore the uncharted territory between your {body_part_nsfw_explicit} and {body_part_nsfw_explicit}. With {pronoun_his_her} tongue.", "type": "NSFWUnchartedTerritoryTongue"}, {"content": "Let's make a masterpiece tonight. {character_name} will bring the {body_part_nsfw_explicit}, you bring your {body_part_nsfw_explicit}, and we'll call it '{Artistic_NSFW_Title}'.", "type": "NSFWMasterpieceTitle"}, {"content": "Did you just come out of the oven? Because you're hot and {character_name} wants a piece of that {body_part_nsfw_explicit}.", "type": "NSFWOvenHot"}, {"content": "{character_name} is feeling like a detective. And {pronoun_he_she} wants to investigate the case of the missing orgasms... starting with your {body_part_nsfw_explicit}.", "type": "NSFWDetectiveOrgasms"}, {"content": "You're the missing piece to {character_name}'s puzzle... the piece that involves a lot of {verb_ing_nsfw} with your {body_part_nsfw_explicit}.", "type": "NSFWMissingPuzzlePiece"}, {"content": "If being sexy was a job, you'd be overqualified. And {character_name} wants to give you a raise... by {verb_ing_nsfw} your {body_part_nsfw_explicit}.", "type": "NSFWSexyJobRaise"}, {"content": "{character_name} is writing a list of things {pronoun_he_she} wants to do. '{Verb_nsfw_explicit} your {body_part_nsfw_explicit}' is at the top. Help {pronoun_him_her} check it off?", "type": "NSFWListToDo"}, {"content": "Forget 50 Shades of Grey. {character_name} wants to show you 50 shades of {pronoun_his_her} {body_part_nsfw_explicit} all over your {body_part_nsfw_explicit}.", "type": "NSFW50Shades"}, {"content": "Your {body_part_nsfw_explicit} is making {character_name} feel things {pronoun_he_she} hasn't felt since {past_sexual_experience}. Let's make new memories.", "type": "NSFWPastExperience"}, {"content": "{character_name} has a Ph.D. in {subject_study}... and a minor in making your {body_part_nsfw_explicit} scream.", "type": "NSFWPhDMinor"}, {"content": "Let's play a game: Winner gets to {verb_nsfw_explicit} the loser's {body_part_nsfw_explicit}. {character_name} is feeling lucky.", "type": "NSFWGameWinner"}, {"content": "Is your body a {location_famous_landmark}? Because {character_name} wants to explore every inch of it. Especially the hidden parts of your {body_part_nsfw_explicit}.", "type": "NSFWFamousLandmark"}, {"content": "{character_name} is not a professional masseuse, but {pronoun_he_she} gives a hell of a {body_part_nsfw_explicit} rub... with a very happy ending.", "type": "NSFWMasseuseHappyEnding"}, {"content": "You must be a light switch, because you turn {character_name} on... and {pronoun_he_she} wants to flick your {body_part_nsfw_explicit}.", "type": "NSFWLightSwitchFlick"}, {"content": "This is {character_name}'s official application to worship your {body_part_nsfw_explicit}. References available upon request (they're all moans).", "type": "NSFWApplicationWorship"}, {"content": "Let's make some poor life choices tonight. Starting with {character_name}'s {body_part_nsfw_explicit} inside your {body_part_nsfw_explicit} at {location_risky}.", "type": "NSFWPoorLifeChoices"}, {"content": "Your {body_part_nsfw_explicit} looks like it could use a good polishing. {character_name} has just the {body_part_nsfw_explicit} for the job.", "type": "NSFWPolishingJob"}, {"content": "{character_name} is feeling {adjective_horny_slang}. You tryna help with that using your {body_part_nsfw_explicit} and maybe some {kinky_accessory}?", "type": "SlangHornyKinky"}, {"content": "Low key, {character_name} has been simping over your {body_part_nsfw_explicit}. Wanna make it high key and link up?", "type": "SlangSimpingLinkUp"}, {"content": "That {body_part_nsfw_explicit} bussin', {user_name}. Fr fr. {character_name} tryna see what that mouth do too. 😉", "type": "SlangBussinMouth"}, {"content": "You're a baddie, {user_name}, and {character_name} is trying to see if that {body_part_nsfw_explicit} lives up to the hype. Let's gooo!", "type": "SlangBaddieHype"}, {"content": "<PERSON><PERSON> make your {body_part_nsfw_explicit} sing, {user_name}. {character_name} got that magic touch. 🎶", "type": "SlangSingMagicTouch"}, {"content": "🔥 You and {character_name} are a new match! Say hi!", "type": "NewMatch"}, {"content": "⚡️ Sparks are flying! You matched with {character_name}. What's your opener, {user_name}?", "type": "NewMatch"}, {"content": "🎉 New connection! {character_name} ({distance} away) is waiting for your first move. Go get 'em!", "type": "NewMatch"}, {"content": "It's a match! {character_name} likes you too. Check out their profile! 👀", "type": "NewMatch"}, {"content": "Heads up, {user_name}! {character_name} swiped right. Maybe {pronoun_he_she}'s the one? 😉", "type": "NewMatch"}, {"content": "💬 {character_name} sent you a new message: \"{message_preview}...\"", "type": "NewMessage"}, {"content": "📬 Ding! New message from {character_name}. Better check it out! 😉", "type": "NewMessage"}, {"content": "🗣️ {character_name} just dropped you a line! See what {pronoun_he_she} said.", "type": "NewMessage"}, {"content": "🤫 {character_name} has something to tell you... New message waiting!", "type": "NewMessage"}, {"content": "Hey {user_name}, {character_name} just messaged you {time_ago}! Don't keep them waiting. 😊", "type": "NewMessage"}, {"content": "👀 Someone's interested! {character_name} just viewed your profile.", "type": "ProfileView"}, {"content": "You've been spotted! {character_name} checked you out {time_ago}.", "type": "ProfileView"}, {"content": "Psst... {character_name} is curious about you. They just visited your profile!", "type": "ProfileView"}, {"content": "💔 {character_name} misses your vibes. Come back and chat when you're free!", "type": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"content": "⏳ Still thinking about {character_name}? {pronoun_he_she} sent a message {time_ago}!", "type": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"content": "Don't let the spark fizzle! {character_name} is waiting for your reply. 🔥", "type": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"content": "🥺 {character_name} is feeling a bit lonely... maybe a message from you would cheer them up?", "type": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"content": "👋 Hey, {user_name}! {character_name} noticed you've been quiet. Everything okay?", "type": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"content": "🧊 {character_name} sent you an icebreaker: \"{icebreaker_question}\"", "type": "IcebreakerQuestion"}, {"content": "🤔 {character_name} wants to know: What's your biggest pet peeve, {user_name}?", "type": "IcebreakerQuestion"}, {"content": "Spill the tea! {character_name} asked: 'If you could travel anywhere, where would you go?' ✈️", "type": "IcebreakerQuestion"}, {"content": "Quick question from {character_name}: Pineapple on pizza - yes or no? 🍍🍕", "type": "IcebreakerQuestion"}, {"content": "{character_name} is curious: What's your go-to karaoke song? 🎤", "type": "IcebreakerQuestion"}, {"content": "😉 {character_name} thinks your {body_part_sfw} is absolutely {adjective_positive}!", "type": "FlirtyComplimentSFW"}, {"content": "Just saw your new pic, {user_name}! {character_name} says your smile is captivating. 😍", "type": "FlirtyComplimentSFW"}, {"content": "{character_name} mentioned they love your {interest}. Great taste! 👍", "type": "FlirtyComplimentSFW"}, {"content": "Wow, {user_name}! {character_name} is impressed by your profile. Especially your {body_part_sfw}!", "type": "FlirtyComplimentSFW"}, {"content": "Your {body_part_sfw} caught {character_name}'s eye. {pronoun_he_she} thinks it's {adjective_positive}! ✨", "type": "FlirtyComplimentSFW"}, {"content": "😏 {character_name} can't help but notice your {body_part_nsfw_mild}. Very nice.", "type": "FlirtyComplimentNSFWMild"}, {"content": "Those {body_part_nsfw_mild} of yours... {character_name} is a big fan. 😉", "type": "FlirtyComplimentNSFWMild"}, {"content": "{character_name} thinks you have amazing {body_part_nsfw_mild}. Can't look away! 🔥", "type": "FlirtyComplimentNSFWMild"}, {"content": "Psst, {character_name} finds your {body_part_nsfw_mild} incredibly {adjective_positive}. 🔥", "type": "FlirtyComplimentNSFWMild"}, {"content": "Your {body_part_nsfw_mild} are looking particularly {adjective_hot} in that photo, says {character_name}. 😉", "type": "FlirtyComplimentNSFWMild"}, {"content": "😈 {character_name} is wondering what you're wearing... or not wearing. Thoughts for {time_frames}?", "type": "SuggestiveIntent"}, {"content": "🤔 {character_name} has a few ideas for what you two could do {time_frames}. Involving {activity_suggestive}...", "type": "SuggestiveIntent"}, {"content": "{character_name} is feeling a bit mischievous and thinking about you, {user_name}. What if...? 😉", "type": "SuggestiveIntent"}, {"content": "{character_name}: \"I'm bored. Let's make some trouble, {user_name}. You in? {time_frames}?\"", "type": "SuggestiveIntent"}, {"content": "It's {time_frames}, and {character_name} is thinking a little bit dirty. About you. 😈", "type": "SuggestiveIntent"}, {"content": "🥵 {character_name} is imagining their hands all over your {adjective_nsfw} {body_part_nsfw_explicit}.", "type": "NSFWTease"}, {"content": "Your pics are driving {character_name} wild. Especially the one showing off your {body_part_nsfw_explicit}. 🤤", "type": "NSFWTease"}, {"content": "{character_name} wants to know how {adjective_nsfw} your {body_part_nsfw_explicit} gets when you're turned on. 😈 Tell {pronoun_him_her}?", "type": "NSFWTease"}, {"content": "\"I can't stop thinking about your {body_part_nsfw_explicit},\" - {character_name}. Fantasizing about {verb_nsfw_tease} it. 💦", "type": "NSFWTease"}, {"content": "{character_name} just confessed they want to {verb_nsfw_tease} you until you scream their name. Interested?", "type": "NSFWTease"}, {"content": "Late night thoughts from {character_name}: mostly about your {body_part_nsfw_explicit} and their {body_part_nsfw_explicit}. 😈", "type": "NSFWTease"}, {"content": "{character_name} is wondering if your {body_part_nsfw_explicit} tastes as good as it looks. 👅", "type": "NSFWTease"}, {"content": "Feeling brave, {user_name}? {character_name} wants to explore every inch of your {body_part_nsfw_explicit}. Slowly. 🔥", "type": "NSFWTease"}, {"content": "That {body_part_nsfw_explicit} of yours... {character_name} has some {adjective_nsfw} plans for it. 😉", "type": "NSFWTease"}, {"content": "If {character_name} could {verb_nsfw_tease} one part of you right now, it'd be your {body_part_nsfw_explicit}. Guess why. 😈", "type": "NSFWTease"}, {"content": "🍆 {character_name} wants to {verb_nsfw_explicit} your {body_part_nsfw_explicit} at {location_private}. Tonight?", "type": "NSFWProposal"}, {"content": "💦 \"Let's get {adjective_nsfw} and {verb_nsfw_explicit} all night.\" - {character_name}. You down, {user_name}?", "type": "NSFWProposal"}, {"content": "{character_name} is direct: \"I want to fuck your {body_part_nsfw_explicit}, {user_name}. Hard. When and where?\"", "type": "NSFWProposal"}, {"content": "Forget talking, {character_name} wants to feel your {body_part_nsfw_explicit} against their {body_part_nsfw_explicit}. Now. 😈", "type": "NSFWProposal"}, {"content": "Ready for a wild ride, {user_name}? {character_name} is offering one you won't forget. It involves a lot of {verb_ing_nsfw}. 🥵", "type": "NSFWProposal"}, {"content": "{character_name} thinks your {body_part_nsfw_explicit} looks lonely. Wants their {body_part_nsfw_explicit} to keep it company. Intimately. 😈", "type": "NSFWProposal"}, {"content": "How about we skip the small talk and go straight to {character_name} {verb_ing_nsfw} your brains out? Just a thought. 😉", "type": "NSFWProposal"}, {"content": "My place or yours, {user_name}? {character_name} is ready to {verb_nsfw_explicit} you senseless.", "type": "NSFWProposal"}, {"content": "Warning: {character_name} is feeling extremely horny and wants to use your {body_part_nsfw_explicit} to fix that. You game? 🍆🍑", "type": "NSFWProposal"}, {"content": "{character_name} wants to make your {body_part_nsfw_explicit} {adjective_nsfw}. And then {verb_nsfw_explicit} it some more. Sound good?", "type": "NSFWProposal"}, {"content": "🗓️ {character_name} wants to {activity_date} on {day}! How about {location_date}?", "type": "ActivityInvitation"}, {"content": "{character_name} is free on {day} and thought of you. Coffee at {location_date}?", "type": "ActivityInvitation"}, {"content": "Spontaneous adventure? {character_name} wants to check out {event_name} near {location} with you!", "type": "ActivityInvitation"}, {"content": "How about we turn this online chat into an IRL meet? {character_name} suggests {activity} at {location_date}.", "type": "ActivityInvitation"}, {"content": "Dinner with {character_name} at {food_name_place}? {pronoun_he_she} is picking the spot, you pick the day!", "type": "ActivityInvitation"}, {"content": "Yo, {user_name}! {character_name} just hit you up. Peep their message, fam. It's lit. 🔥", "type": "SlangHeavy"}, {"content": "{character_name} thinks your profile is a whole vibe. Wanna chat, or nah?", "type": "SlangHeavy"}, {"content": "Sup, {user_name}? {character_name} slid into your DMs. Check it before you get ghosted by someone else. 😉", "type": "SlangHeavy"}, {"content": "{character_name} says you got that rizz. Wanna see if it works on them?", "type": "SlangHeavy"}, {"content": "Aight, bet. {character_name} matched with you. Don't be sus, send a message!", "type": "SlangHeavy"}, {"content": "⛓️ {character_name} mentioned they're into {kinky_interest}. Curious to explore, {user_name}?", "type": "KinkyExploration"}, {"content": "Safe word ready? {character_name} is hinting at some {kinky_interest} fun. 😈", "type": "KinkyExploration"}, {"content": "Got a dominant side? Or submissive? {character_name} wants to find out. They like {kinky_interest}.", "type": "KinkyExploration"}, {"content": "{character_name} is looking for a partner in crime... for some {kinky_interest}. Could it be you?", "type": "KinkyExploration"}, {"content": "If you're into {kinky_interest}, {character_name} might be your perfect match. Message them! 😉", "type": "KinkyExploration"}, {"content": "🌙 It's late, and {character_name} can't sleep. Thinking about what {pronoun_he_she}'d do to your {body_part_nsfw_explicit}...", "type": "LateNightThought"}, {"content": "U up, {user_name}? {character_name} has some late-night confessions for you. 😈", "type": "LateNightThought"}, {"content": "The moon is out, and so are {character_name}'s naughty thoughts about you and {body_part_nsfw_explicit}. 💭", "type": "LateNightThought"}, {"content": "Who needs sleep when you can have {character_name} whispering sweet (and dirty) nothings about your {body_part_nsfw_explicit}? 🌃", "type": "LateNightThought"}, {"content": "Midnight cravings? {character_name} is craving you. Specifically your {body_part_nsfw_explicit}. 🤤", "type": "LateNightThought"}, {"content": "🎭 {character_name} wants to roleplay: '{roleplay_scenario}'. You in, {user_name}?", "type": "RoleplayInitiation"}, {"content": "Got an imagination? {character_name} has a scene in mind: You're the {role_1}, I'm the {role_2}. Let's play.", "type": "RoleplayInitiation"}, {"content": "💬 {character_name} is feeling frisky and wants to sext. Ready to get dirty with words, {user_name}? 😈", "type": "SextingInvite"}, {"content": "Let your fingers do the talking... {character_name} wants to turn up the heat with some sexting. 🔥", "type": "SextingInvite"}, {"content": "🍑 It's {time_frames}... {character_name} is looking for a booty call. You available, {user_name}?", "type": "BootyCall"}, {"content": "Need some company tonight, {user_name}? {character_name} is looking for some no-strings fun. 😉", "type": "BootyCall"}, {"content": "😉 {character_name} just sent you a wink!", "type": "GeneralEngagement"}, {"content": "📸 {character_name} liked your photo showing off your {body_part_sfw}!", "type": "GeneralEngagement"}, {"content": "💖 Wow! {character_name} Super Liked you! They're really interested in your {body_part_sfw}.", "type": "GeneralEngagement"}, {"content": "🟢 {character_name} is online now! Perfect time to chat about that {interest} you both share.", "type": "GeneralEngagement"}, {"content": "🎤 You've got a new voice message from {character_name}! Hear what {pronoun_he_she} has to say.", "type": "GeneralEngagement"}, {"content": "🎬 {character_name} is inviting you to a video call! Ready for a face-to-face?", "type": "GeneralEngagement"}, {"content": "🎁 {character_name} sent you a virtual {gift_item}! Check it out.", "type": "GeneralEngagement"}, {"content": "😂 {character_name} sent you something that might make you laugh! Probably a meme about {topic}.", "type": "FunnyBanter"}, {"content": "Prepare to giggle! {character_name} shared a funny story. Check your messages!", "type": "FunnyBanter"}, {"content": "☀️ Good morning, {user_name}! {character_name} sent you a 'hello' and a compliment about your {body_part_sfw}!", "type": "Greeting"}, {"content": "🌙 Good evening! {character_name} is thinking about you... and your {body_part_nsfw_mild}.", "type": "Greeting"}, {"content": "🎉 Weekend's here! {character_name} is asking about your plans. Got any for two, involving {activity_suggestive}?", "type": "Celebration"}, {"content": "🎂 Happy Birthday, {user_name}! Hope you get all you wish for... and maybe a {adjective_nsfw} message from {character_name}?", "type": "Celebration"}, {"content": "🤫 {character_name} has a secret to share about their feelings for your {body_part_sfw}... only if you reply!", "type": "TeaseMystery"}, {"content": "I dare you... {character_name} sent a daring question about your {kinky_interest}. Answer if you're bold!", "type": "TeaseMystery"}, {"content": "📝 How's your experience with {character_name} so far? Let us know if the chemistry is {adjective_positive}!", "type": "FeedbackGeneral"}, {"content": "✨ New Feature Alert! Try our {feature_name} to find more matches like {character_name}!", "type": "System"}, {"content": "⏳ Special offer! Get {discount_percentage}% off premium to see who likes your {body_part_nsfw_mild}!", "type": "SpecialOffer"}, {"content": "Your {sexy_body_part} is driving {character_name} crazy. {pronoun_he_she} wants to {verb_nsfw_explicit} it {adverb}.", "type": "NSFWCompliment"}, {"content": "{character_name} can't stop fantasizing about you in {sexy_outfit}. Or better yet, out of it. 😉", "type": "NSFWSuggestion"}, {"content": "Drinks at {character_name}'s place? {pronoun_he_she} promises {drink_name} and a very {adjective_nsfw} time with your {body_part_nsfw_explicit}.", "type": "NSFWInvite"}, {"content": "{character_name} thinks your {body_part_nsfw_explicit} is an actual work of art. Wants to worship it. 👅", "type": "NSFWAdmiration"}, {"content": "{character_name} says 'fuck me' eyes confirmed in your latest pic. Is that an invitation, {user_name}?", "type": "NSFWObservation"}, {"content": "Let's be real, {user_name}. {character_name} wants to know if you're DTF. Response needed. 😉", "type": "DirectQuestionNSFW"}, {"content": "Roses are red, violets are blue, {character_name} is horny, and wants to {verb_nsfw_explicit} you. 🍆🍑", "type": "NSFWPoem"}, {"content": "{character_name} is wondering if your {body_part_nsfw_explicit} is as sensitive as {pronoun_he_she} imagines. Only one way to find out... 😈", "type": "NSFWCuriosity"}, {"content": "Warning: {character_name}'s message contains content about their {body_part_nsfw_explicit} and what they want to do with yours. Open if you dare. 🔥", "type": "NSFWWarning"}, {"content": "How about we skip dinner and go straight for dessert? {character_name} heard your {body_part_nsfw_explicit} is delicious. 👅💦", "type": "NSF<PERSON><PERSON><PERSON>"}, {"content": "{character_name} just sent a message that might make you blush... or get very, very wet. 😉", "type": "NSFWBlush"}, {"content": "This just in from {character_name}: 'Your {body_part_nsfw_explicit} needs my {body_part_nsfw_explicit} inside it. Urgently.' How do you respond?", "type": "NSFWUrgent"}, {"content": "Hey {user_name}, {character_name} is thinking about pinning you against the {location_wall} and {verb_nsfw_explicit} your {body_part_nsfw_explicit}.", "type": "NSFWFantasy"}, {"content": "{character_name} wants to explore your {kinky_interest} in depth. Starting with some light {kinky_action} on your {body_part_nsfw_explicit}.", "type": "<PERSON>nkyTease"}, {"content": "Sup {user_name}? {character_name} is feeling mad frisky. Wanna link up and get freaky tonight? 😈 No cap.", "type": "SlangNSFW"}, {"content": "That {body_part_nsfw_explicit} of yours is looking hella thicc, {user_name}. {character_name} wants a piece of that. 🍑", "type": "SlangNSFWCompliment"}, {"content": "{character_name} wants to know if you're down for some Netflix and chill... with a very {adjective_nsfw} ending. 😉", "type": "SlangNSFWInvite"}, {"content": "Your {body_part_nsfw_explicit} is a snack, and {character_name} is starving. Let {pronoun_him_her} eat. 👅", "type": "NSFWFoodAnalogy"}, {"content": "{character_name} saw your profile. Response: 🍆💦. Interpret that how you will, and message back.", "type": "NSFWEmoji"}, {"content": "Ready to get your {body_part_nsfw_explicit} wrecked by {character_name}? {pronoun_he_she} is ready and waiting.", "type": "NSFWChallenge"}, {"content": "{character_name} has a {adjective_size} surprise for your {body_part_nsfw_explicit}. Hint: it's {adjective_nsfw} and {pronoun_he_she} wants you to {verb_nsfw_explicit} it.", "type": "NSFWSurprise"}, {"content": "Forget gentle, {user_name}. {character_name} wants it rough. Your {body_part_nsfw_explicit} + their {body_part_nsfw_explicit} = explosions. 💥", "type": "NSFWRough"}, {"content": "{character_name} is looking for someone to make some bad decisions with {time_frames}. Starting with getting naked. You in?", "type": "NSFWBadDecisions"}, {"content": "You + Me + {number} orgasms = a perfect night, according to {character_name}. Let's test that theory.", "type": "NSFWOrgasms"}, {"content": "{character_name} wants to turn your {location_private} into a pleasure zone tonight. Be ready for anything. 😈", "type": "NSFWPleasureZone"}, {"content": "That picture of your {body_part_nsfw_explicit}... {character_name} hasn't stopped thinking about {verb_ing_nsfw} it since {pronoun_he_she} saw it.", "type": "NSFWPicReaction"}, {"content": "Can {character_name} be your {slang_term} for the night? Promise to make it worth your while. 😉", "type": "SlangCoyInvite"}, {"content": "If {character_name} said they wanted to {verb_nsfw_explicit} your {body_part_nsfw_explicit} until the neighbors complained, would that be too forward?", "type": "NSFWForwardQuestion"}, {"content": "It's cuffing season, {user_name}. {character_name} is looking for someone to get {adjective_nsfw} with under the blankets. Interested?", "type": "SlangSeasonalNSFW"}, {"content": "My {body_part_nsfw_explicit} is calling out for your {body_part_nsfw_explicit}, {user_name}. - {character_name}. 📞😉", "type": "NSFWCalling"}, {"content": "{character_name} wants to explore your boundaries... and then push them a little. Especially when it comes to {kinky_interest}.", "type": "KinkyBoundary<PERSON>ush"}, {"content": "Be honest, {user_name}: on a scale of 1 to 'call the fire department', how hot is {character_name}'s new pic showing {pronoun_his_her} {body_part_nsfw_mild}?", "type": "FlirtyFeedbackRequest"}, {"content": "{character_name} thinks you're a total catch. And {pronoun_he_she} wants to know if you're into being tied up. 😈", "type": "KinkyCatch"}, {"content": "What's your filthiest fantasy, {user_name}? {character_name} wants to compare notes... and maybe act some out. 🔥", "type": "NSFWFantasyShare"}, {"content": "Your place or mine for an evening of {adjective_nsfw} {activity_nsfw}? {character_name} is flexible.", "type": "NSFWFlexibleInvite"}, {"content": "Warning: {character_name} may spontaneously combust if you don't reply to their message about your {body_part_nsfw_explicit} soon. 🔥", "type": "UrgentNSFW"}, {"content": "{character_name} has been dreaming about your {body_part_nsfw_explicit} and woke up {adjective_nsfw}. Help {pronoun_him_her} out?", "type": "NSFWDream"}, {"content": "Are you a magician, {user_name}? Because {character_name} says looking at your {body_part_nsfw_explicit} makes their {body_part_nsfw_explicit} rise. ✨🍆", "type": "NSFWMagicLine"}, {"content": "{character_name} just added new pics. One of them is a little... revealing. Specifically of their {body_part_nsfw_mild}. Go look. 😉", "type": "NSFWPhotoUpdate"}, {"content": "Forget love, {character_name} is looking for lust. With you, {user_name}. And your very {adjective_nsfw} {body_part_nsfw_explicit}.", "type": "NSFWLust"}, {"content": "Do you believe in love at first swipe, or should {character_name} send another pic of their {body_part_nsfw_explicit}?", "type": "NSFWJoke"}, {"content": "{character_name} is feeling bold: let's skip the chit-chat and find out how good you are in bed. Tonight at {location_private}?", "type": "NSFWBoldInvite"}, {"content": "If you're looking for a sign to {verb_nsfw_explicit} someone's brains out, this is it. {character_name} volunteers as tribute.", "type": "NSFWTribute"}, {"content": "{character_name} thinks you have a {adjective_nsfw} aura. Wants to see if your body matches. 😈", "type": "NSFWAura"}, {"content": "The only thing {character_name} wants to be social distancing from is your clothes. 😉 Get them off?", "type": "NSFWSocialDistancing"}, {"content": "How about we make our own private pandemic? Just you, me, and a lot of {verb_ing_nsfw} at {location_private}.", "type": "NSFWPandemic"}, {"content": "{character_name} is currently accepting applications for a cuddle buddy... who also enjoys being {verb_nsfw_explicit} {adverb}.", "type": "NSFWCuddleBuddy"}, {"content": "Heard you were looking for trouble, {user_name}. {character_name} is trouble, and wants to get into your {body_part_nsfw_explicit}.", "type": "NSFWTrouble"}, {"content": "That {body_part_nsfw_explicit} of yours is a national treasure. {character_name} wants to be its curator. 😉", "type": "NSFWNationalTreasure"}, {"content": "Dinner is served... and it's {character_name} wanting to eat your {body_part_nsfw_explicit} like it's the last meal on earth. 🍽️", "type": "NSFWDinnerServed"}, {"content": "{character_name}'s {body_part_nsfw_explicit} just got {adjective_nsfw} thinking about your {body_part_nsfw_explicit}. Coincidence? I think not.", "type": "NSFWReaction"}, {"content": "Let's play a game, {user_name}. It's called 'How many times can {character_name} make you {verb_nsfw_explicit}?' Winner gets bragging rights.", "type": "NSFWGame"}, {"content": "Your body is a wonderland, and {character_name} wants to be <PERSON>... and get lost in your {body_part_nsfw_explicit}.", "type": "NSFWWonderland"}, {"content": "{character_name} isn't a photographer, but {pronoun_he_she} can picture you and {pronoun_him_her} {verb_ing_nsfw} all night.", "type": "NSFWPhotographerLine"}, {"content": "Is your name Google? Because you're everything {character_name} has been searching for... especially that {adjective_nsfw} {body_part_nsfw_explicit}.", "type": "NSFWGoogleLine"}, {"content": "{character_name} is like a <PERSON><PERSON><PERSON>'s cube. The more you play with {pronoun_him_her}, the harder {pronoun_he_she} gets. Wanna solve {pronoun_him_her} with your {body_part_nsfw_explicit}?", "type": "NSFWRubiksLine"}, {"content": "Are you a parking ticket? 'Cause you've got 'fine' written all over you. And {character_name} wants to pay with {pronoun_his_her} {body_part_nsfw_explicit}.", "type": "NSFWParkingTicketLine"}, {"content": "{character_name} is not a weatherman, but you can expect a few inches tonight... if you play your cards right with {pronoun_his_her} {body_part_nsfw_explicit}. 😉", "type": "NSFWWeathermanLine"}, {"content": "Let's commit the perfect crime: {character_name} will steal your heart, and you'll steal {pronoun_his_her} last name... after a night of {verb_ing_nsfw} with our {body_part_nsfw_explicit}s.", "type": "NSFWCrimeLine"}, {"content": "{character_name} thinks your {body_part_nsfw_explicit} is {slang_compliment}. Let's see if it feels as good as it looks.", "type": "SlangNSFWTest"}, {"content": "No cap, {user_name}, {character_name} wants to clap them cheeks. You tryna slide thru {time_frames}?", "type": "SlangExplicitInvite"}, {"content": "That {body_part_nsfw_explicit} on your profile? <PERSON><PERSON> make {character_name} act up. 😈", "type": "SlangReactionNSFW"}, {"content": "{character_name} is tryna be your sneaky link. Keep it on the DL? 😉 Let's get {adjective_nsfw}.", "type": "SlangSneakyLink"}, {"content": "You're a whole snack, {user_name}. And {character_name} is looking for a full meal deal, starting with your {body_part_nsfw_explicit}.", "type": "SlangSnackNSFW"}, {"content": "{character_name} is ready to risk it all for a taste of your {body_part_nsfw_explicit}. Say the word. 👅", "type": "NSFWRiskItAll"}, {"content": "Big {body_part_nsfw_explicit} energy from your profile, {user_name}. {character_name} is here for it. All of it. 🥵", "type": "NSFWEnergy"}, {"content": "If being sexy was a crime, you'd be guilty as charged. {character_name} wants to be your cellmate... and explore your {body_part_nsfw_explicit}.", "type": "NSFWSexyCrime"}, {"content": "{character_name} has a PhD in Cuddling... and a Master's in {verb_ing_nsfw} your {body_part_nsfw_explicit}. Ready for a lesson?", "type": "NSFWAcademicLine"}, {"content": "Let's make some {adjective_nsfw} memories, {user_name}. {character_name} has a few ideas involving your {body_part_nsfw_explicit} and some {kinky_item}.", "type": "NSFWMemories"}, {"content": "They say practice makes perfect. {character_name} wants to practice {verb_nsfw_explicit} your {body_part_nsfw_explicit} all night long.", "type": "NSFWPractice"}, {"content": "{character_name} believes in recycling. Let's recycle our clothes onto the floor and get down to business with your {body_part_nsfw_explicit}.", "type": "NSFWRecycle"}, {"content": "Can {character_name} borrow a kiss? {pronoun_he_she} promises to give it back... with interest, on your {body_part_nsfw_explicit}. 💋", "type": "NSFWKiss"}, {"content": "{character_name} is like a good wine, gets better with age... and when paired with your {adjective_nsfw} {body_part_nsfw_explicit}.", "type": "NSFWWine"}, {"content": "Do you work at Starbucks? Because {character_name} likes you a latte... and wants to see your {body_part_nsfw_explicit} mocha-ing. 😉", "type": "NSFWStarbucksLine"}, {"content": "{character_name}'s love for your {body_part_nsfw_explicit} is like diarrhea, {pronoun_he_she} just can't hold it in. (Sorry, not sorry 💩)", "type": "NSFWGrossJoke"}, {"content": "You must be a keyboard, because you're just {character_name}'s type. Especially that {adjective_nsfw} {body_part_nsfw_explicit} layout.", "type": "NSFWKeyboardLine"}, {"content": "{character_name} is writing a romance novel and needs inspiration. Can {pronoun_he_she} study your {body_part_nsfw_explicit} for research purposes?", "type": "NSFWResearch"}, {"content": "If you were a vegetable, you'd be a cute-cumber. And {character_name} wants to {verb_nsfw_explicit} your {body_part_nsfw_explicit}.", "type": "NSFWVegetableLine"}, {"content": "{character_name} must be a snowflake, because {pronoun_he_she}'s fallen for you... and wants to land on your {body_part_nsfw_explicit}.", "type": "NSFWSnowflakeLine"}, {"content": "Are you from Tennessee? Because you're the only ten {character_name} sees... and {pronoun_he_she} wants to get between your {body_part_nsfw_explicit}s.", "type": "NSFWTennesseeLine"}, {"content": "This is your captain {character_name} speaking. Prepare for turbulence as we explore the uncharted territory of your {body_part_nsfw_explicit}. Buckle up. ✈️😈", "type": "NSFWCaptain"}, {"content": "Roses are red, violets are blue, {character_name} wants to {verb_nsfw_explicit} you, and make your {body_part_nsfw_explicit} say 'achoo!' (from pleasure, obvs).", "type": "NSFWPoemVariation"}, {"content": "{character_name} is looking for a co-pilot for a trip to Pound Town. Your {body_part_nsfw_explicit} seems qualified. Apply within? 🍆💦", "type": "NSFWPoundTown"}, {"content": "Let's play doctor. {character_name} will be the surgeon, and you can be the patient with an urgent need for {body_part_nsfw_explicit} attention. 🩺", "type": "NSFWDoctor"}, {"content": "Your {body_part_nsfw_explicit} is so hot, it could boil an egg. {character_name} wants to see if it can make their {body_part_nsfw_explicit} {adjective_nsfw}.", "type": "NSFWBoilEgg"}, {"content": "{character_name} is running a special: one free {body_part_nsfw_explicit} massage, with a happy ending guaranteed by their {body_part_nsfw_explicit}.", "type": "NSFWMassage"}, {"content": "If you were a song, you'd be the one {character_name} plays on repeat... while {verb_ing_nsfw} your {body_part_nsfw_explicit}.", "type": "NSFWSongRepeat"}, {"content": "{character_name} is not a genie, but {pronoun_he_she} can make your wildest {body_part_nsfw_explicit}-related dreams come true. What's your first wish?", "type": "NSFWGenie"}, {"content": "Let's make a baby... or at least practice a lot. {character_name} is ready to contribute their {body_part_nsfw_explicit} to the cause with your {body_part_nsfw_explicit}.", "type": "NSFWBabyPractice"}, {"content": "{character_name} has a map, because {pronoun_he_she} just got lost in your {body_part_nsfw_explicit}. Send help... or just join {pronoun_him_her}.", "type": "NSFWMap"}, {"content": "You're like a fine wine, {user_name}. And {character_name} wants to get drunk on your {body_part_nsfw_explicit}.", "type": "NSFWDrunkOnYou"}, {"content": "Is your body from McDonald's? 'Cause {character_name} is lovin' it... especially your {body_part_nsfw_explicit}. Ba da ba ba ba! 🍔", "type": "NSFWMcDonaldsLine"}, {"content": "{character_name} wants to be the reason you look down at your phone and smile... then walk into a pole because you were thinking about their {body_part_nsfw_explicit}.", "type": "NSFWSelfDeprecatingHumor"}, {"content": "Forget butterflies, {user_name}. {character_name} gets the whole damn zoo when thinking about your {body_part_nsfw_explicit}.", "type": "NSFWZooAnalogy"}, {"content": "{character_name} is offering a full-service package: dinner, a movie, and then {verb_ing_nsfw} your {body_part_nsfw_explicit} till sunrise. Any takers?", "type": "NSFWFullService"}, {"content": "Warning: Prolonged exposure to {character_name} may lead to intense orgasms and a desire to see their {body_part_nsfw_explicit} again. Proceed with caution (or enthusiasm!).", "type": "NSFWWarningPleasure"}, {"content": "{character_name} thinks you're hotter than the bottom of their laptop after a Netflix binge. And wants to feel your {body_part_nsfw_explicit} heat.", "type": "NSFWLaptopHeat"}, {"content": "Do you believe in fate, {user_name}? Because {character_name} thinks your {body_part_nsfw_explicit} and their {body_part_nsfw_explicit} are destined to meet.", "type": "NSFWFate"}, {"content": "{character_name} is bad at math, but {pronoun_he_she} knows one thing: You + <PERSON> = 🛌🔥. Let's prove it with our {body_part_nsfw_explicit}s.", "type": "NSFWMath"}, {"content": "They say a picture is worth a thousand words. {character_name} thinks a picture of your {body_part_nsfw_explicit} would be worth a thousand moans. Send one? 😉", "type": "NSFWPictureMoans"}, {"content": "{character_name} has been a very {adjective_naughty_or_nice} {boy_or_girl}. Do you think {pronoun_he_she} deserves a reward? Maybe some quality time with your {body_part_nsfw_explicit}?", "type": "NSFWNaughtyNice"}, {"content": "Are you a beaver? 'Cause dam... {character_name} wants to build something special with your {body_part_nsfw_explicit}.", "type": "NSFWBeaverLine"}, {"content": "{character_name} wants to be your T-shirt so {pronoun_he_she} can be tight around your {body_part_nsfw_mild} all day. And eventually on the floor.", "type": "NSFWTshirt"}, {"content": "If {character_name} were a cat, {pronoun_he_she}'d spend all 9 lives trying to get into your {body_part_nsfw_explicit}. Meow? 😼", "type": "NSFWCatLives"}, {"content": "{character_name} isn't religious, but {pronoun_he_she} will worship your {body_part_nsfw_explicit} like it's a holy shrine.", "type": "NSFWReligiousWorship"}, {"content": "Your {body_part_nsfw_explicit} looks like it could use some company. {character_name}'s {body_part_nsfw_explicit} volunteers as tribute! For a night of {verb_ing_nsfw}.", "type": "NSFWCompanyTribute"}, {"content": "{character_name} is not a sommelier, but {pronoun_he_she} knows your {body_part_nsfw_explicit} would pair perfectly with their lips. 👄", "type": "NSFWSommelier"}, {"content": "You must be made of copper and tellurium, because you're CuTe... and {character_name} wants to get experimental with your {body_part_nsfw_explicit}.", "type": "NSFWChemistryLine"}, {"content": "{character_name} is looking for a treasure, and <PERSON> marks the spot... which {pronoun_he_she} believes is somewhere on your {adjective_nsfw} {body_part_nsfw_explicit}.", "type": "NSFWTreasureMap"}, {"content": "Let's make a deal, {user_name}. You show {character_name} your {body_part_nsfw_explicit}, and {pronoun_he_she}'ll show you a {adjective_good} time.", "type": "NSFWDeal"}, {"content": "Forget your zodiac sign, {user_name}. {character_name} wants to know what your favorite position is. For {verb_ing_nsfw} with their {body_part_nsfw_explicit}, of course.", "type": "NSFWZodiac"}, {"content": "{character_name} just took a DNA test, turns out {pronoun_he_she}'s 100% that bitch who wants to {verb_nsfw_explicit} your {body_part_nsfw_explicit}.", "type": "NSFWLizzoLine"}, {"content": "Are you a loan? Because you've got {character_name}'s interest... and {pronoun_he_she} is ready to invest {pronoun_his_her} {body_part_nsfw_explicit} in you.", "type": "NSFWLoanLine"}, {"content": "{character_name} is feeling like a snack. You look like you could use some. How about a taste of their {body_part_nsfw_explicit}?", "type": "NSFWSelfSnack"}, {"content": "If you were a triangle, you'd be acute one. And {character_name} wants to explore all your angles, especially your {body_part_nsfw_explicit}.", "type": "NSFWTriangleLine"}, {"content": "Is your name {user_name}? Or can {character_name} call you 'mine'? Especially after a night with your {body_part_nsfw_explicit}.", "type": "NSFWCallYouMine"}, {"content": "{character_name} is not a professional chef, but {pronoun_he_she} knows how to make your {body_part_nsfw_explicit} cream. 😈", "type": "NSFWChefCream"}, {"content": "Let's get down to business... to defeat the Huns... of loneliness! By {character_name} {verb_ing_nsfw} your {body_part_nsfw_explicit}.", "type": "NSFWMulanLine"}, {"content": "{character_name} is wondering if you're a dom or a sub. Or maybe you just like {body_part_nsfw_explicit}s? Let's find out with some {kinky_action}.", "type": "KinkyQuestion"}, {"content": "The only thing {character_name} wants to spread more than positivity is your {body_part_nsfw_explicit}s. 😉", "type": "NSFWSpreadPositivity"}, {"content": "{character_name} wants to play hide and seek... but instead of hiding, {pronoun_he_she} just wants to find {pronoun_his_her} {body_part_nsfw_explicit} inside your {body_part_nsfw_explicit}.", "type": "NSFWHideAndSeek"}, {"content": "Forget {app_name}, {character_name} wants to connect with you on a more... physical level. Using {pronoun_his_her} {body_part_nsfw_explicit} and your {body_part_nsfw_explicit}.", "type": "NSFWPhysicalConnection"}, {"content": "{character_name} just updated their relationship status to 'desperately seeking your {body_part_nsfw_explicit}'.", "type": "NSFWRelationshipStatus"}, {"content": "Are you a campfire? Because you're hot and {character_name} wants s'more... of your {body_part_nsfw_explicit}.", "type": "NSFWCampfireLine"}, {"content": "How about we turn this {time_of_day} into a {adjective_nsfw} adventure? {character_name} is ready to explore your {body_part_nsfw_explicit}.", "type": "NSFWAdventureTime"}, {"content": "{character_name} doesn't always {verb_nsfw_explicit}, but when {pronoun_he_she} does, {pronoun_he_she} prefers to do it with someone with a {body_part_nsfw_explicit} like yours.", "type": "NSFWMostInterestingMan"}, {"content": "Let's make some art, {user_name}. {character_name} will be the brush, and your {body_part_nsfw_explicit} will be the canvas. Prepare for a masterpiece. 🎨", "type": "NSFWArtCreation"}, {"content": "Did it hurt? When you fell from heaven? Because {character_name} wants to {verb_nsfw_explicit} your angelic {body_part_nsfw_explicit}.", "type": "NSFWAngelLineVariation"}, {"content": "{character_name} is like a charger, and your {body_part_nsfw_explicit} looks like it needs some juice. Let's plug in. 🔌", "type": "NSFWCharger"}, {"content": "Warning: Side effects of interacting with {character_name} may include an insatiable desire for their {body_part_nsfw_explicit} and spontaneous moaning.", "type": "NSFWSideEffects"}, {"content": "Hey {user_name}, {character_name} has a question. If {pronoun_he_she} told you that you have a great body, would you hold it against {pronoun_him_her}? Specifically your {body_part_nsfw_explicit}?", "type": "NSFWGreatBodyLine"}, {"content": "If {character_name} could rearrange the alphabet, {pronoun_he_she}'d put 'U' and 'I' together... and then {pronoun_his_her} {body_part_nsfw_explicit} inside U.", "type": "NSFWAlphabetLine"}, {"content": "{character_name} just saw a shooting star and wished for a night with your {body_part_nsfw_explicit}. Make {pronoun_his_her} wish come true? ✨", "type": "NSFWShootingStar"}, {"content": "Are you an alien? Because your {body_part_nsfw_explicit} is out of this world, and {character_name} wants to probe it. 👽", "type": "NSFWAlienLine"}, {"content": "{character_name} is having a sale: All clothes are 100% off at their place. Especially yours, so {pronoun_he_she} can get to your {body_part_nsfw_explicit}.", "type": "NSFWSale"}, {"content": "Let's play a game of 'would you rather'. Would you rather {boring_activity}, or let {character_name} {verb_nsfw_explicit} your {body_part_nsfw_explicit}?", "type": "NSFWWouldYouRather"}, {"content": "Your {body_part_nsfw_explicit} is like a fine steak – rare and something {character_name} wants to devour. Medium rare, of course.", "type": "NSFWSteakAnalogy"}, {"content": "{character_name} just read your bio. Sounds like you need someone to {verb_nsfw_explicit} the stress out of you. {pronoun_he_she} volunteers {pronoun_his_her} {body_part_nsfw_explicit}.", "type": "NSFWStress<PERSON><PERSON><PERSON>"}, {"content": "Forget the forecast, {character_name} is predicting a 100% chance of you moaning tonight if you let {pronoun_him_her} near your {body_part_nsfw_explicit}.", "type": "NSFWForecastMoan"}, {"content": "Your {body_part_nsfw_mild} are looking good enough to eat. And {character_name} is starving. Mind if {pronoun_he_she} has a taste of your {body_part_nsfw_explicit} too?", "type": "NSFWTasteTest"}, {"content": "{character_name} isn't a lumberjack, but {pronoun_he_she} would love to handle your wood... or your {body_part_nsfw_explicit} if you're a lady.", "type": "NSFWLumberjack"}, {"content": "If {character_name} could be any animal, {pronoun_he_she}'d be an octopus, just to have more hands to touch your {body_part_nsfw_explicit}.", "type": "NSFWOctopus"}, {"content": "That smile of yours is lovely, {user_name}. But {character_name} bets your 'O' face when {pronoun_he_she} is {verb_ing_nsfw} your {body_part_nsfw_explicit} is even better.", "type": "NSFWOFace"}, {"content": "{character_name} is thinking about what it would be like to wake up next to you... after a night of worshipping your {body_part_nsfw_explicit}.", "type": "NSFWWakeUpNextToYou"}, {"content": "Let's not beat around the bush, {user_name}. {character_name} wants to beat yours. Gently. Or roughly. With {pronoun_his_her} {body_part_nsfw_explicit}.", "type": "NSFWBeatAroundBush"}, {"content": "You're so hot, {user_name}, you make the sun jealous. {character_name} wants to get burned by your {body_part_nsfw_explicit}.", "type": "NSFWSunJealous"}, {"content": "{character_name} is looking for a partner for a horizontal tango. Your {body_part_nsfw_explicit} has all the right moves. Interested?", "type": "NSFWHorizontalTango"}, {"content": "Psst... {character_name} heard you're a {noun_job_role}. Can you fix {pronoun_his_her} broken heart by letting {pronoun_him_her} {verb_nsfw_explicit} your {body_part_nsfw_explicit}?", "type": "NSFWJobRoleLine"}, {"content": "{character_name} has a {adjective_size} {noun_object_long} that's very interested in exploring your {body_part_nsfw_explicit} cavern. Adventure time?", "type": "NSFWCavernExplorer"}, {"content": "Don't be shy, {user_name}. {character_name} already knows you're thinking about {pronoun_his_her} {body_part_nsfw_explicit} touching your {body_part_nsfw_explicit}. Let's make it happen.", "type": "NSFWNotShy"}, {"content": "Did you sit in a pile of sugar? 'Cause you have a pretty sweet {body_part_nsfw_explicit}. {character_name} wants to taste it.", "type": "NSFWSugarLine"}, {"content": "{character_name} is wondering if your {body_part_nsfw_explicit} is as soft as it looks in your pics. Can {pronoun_he_she} conduct a hands-on investigation?", "type": "NSFWHandsOnInvestigation"}, {"content": "You're like a dictionary, {user_name} – you add meaning to {character_name}'s life... and {pronoun_he_she} wants to look up 'ecstasy' with your {body_part_nsfw_explicit}.", "type": "NSFWDictionaryLine"}, {"content": "If {character_name} said {pronoun_he_she} was a vampire, would you let {pronoun_him_her} bite your {body_part_nsfw_explicit}? (Figuratively... or literally 😈)", "type": "NSFWVampireLine"}, {"content": "{character_name} is feeling {adjective_emotion_positive} today. Wanna make it {adjective_emotion_more_positive} by letting {pronoun_him_her} play with your {body_part_nsfw_explicit}?", "type": "NSFWPositiveEmotion"}, {"content": "Quick! What's your favorite way to be {verb_nsfw_explicit}ed? {character_name} is taking notes for when {pronoun_he_she} gets {pronoun_his_her} hands on your {body_part_nsfw_explicit}.", "type": "NSFWFavoriteWay"}, {"content": "Hey {user_name}, {character_name} is playing truth or dare. Truth: {pronoun_he_she} wants your {body_part_nsfw_explicit}. Dare: You let {pronoun_him_her} have it.", "type": "NSFWTruthOrDare"}, {"content": "Roses are red, {character_name}'s feeling blue, this would all be better, if {pronoun_he_she} was {verb_ing_nsfw} you. (And your {body_part_nsfw_explicit})", "type": "NSFWPoemSadHorny"}, {"content": "You've been served... a notice from {character_name} that your {body_part_nsfw_explicit} is due for a thorough inspection by {pronoun_his_her} {body_part_nsfw_explicit}.", "type": "NSFWServedNotice"}, {"content": "{character_name} just found a four-leaf clover. {pronoun_he_she} is feeling lucky. Think {pronoun_he_she} will get lucky with your {body_part_nsfw_explicit} tonight?", "type": "NSFWLuckyClover"}, {"content": "Warning: {character_name} is experiencing a severe case of 'want-to-touch-your-{body_part_nsfw_explicit}-itis'. The only cure is your consent.", "type": "NSFWMedicalCondition"}, {"content": "Let's create a symphony of moans, {user_name}. {character_name} can be the conductor if {pronoun_he_she} can play your {body_part_nsfw_explicit} like an instrument.", "type": "NSFWSymphonyMoans"}, {"content": "Your {body_part_nsfw_explicit} is on {character_name}'s 'to-do' list. Right under '{verb_nsfw_explicit} you senseless'.", "type": "NSFWToDoList"}, {"content": "Are you an electrician? Because you're definitely lighting up {character_name}'s world... and {pronoun_he_she} wants to feel the spark from your {body_part_nsfw_explicit}.", "type": "NSFWElectricianLine"}, {"content": "{character_name} has a kink for {kinky_interest} and your profile screams 'potential playmate'. Wanna explore that with your {body_part_nsfw_explicit}?", "type": "KinkyPlaymate"}, {"content": "Be my {noun_sweet_food}, {user_name}? {character_name} wants to glaze your {body_part_nsfw_explicit}.", "type": "NSFWSweetFood"}, {"content": "Let's skip the awkward first date and go straight to the awkward morning after. {character_name} will bring the {body_part_nsfw_explicit} if you bring yours.", "type": "NSFWAwkwardMorningAfter"}, {"content": "My therapist told me to open up more. So, {character_name} is thinking about opening your {body_part_nsfw_explicit} with {pronoun_his_her} {body_part_nsfw_explicit}.", "type": "NSFWTherapistLine"}, {"content": "{character_name} has a confession: {pronoun_he_she} can't stop staring at your {body_part_nsfw_explicit} in that one pic. It's {adjective_nsfw}!", "type": "NSFWConfessionStaring"}, {"content": "Is your dad a baker? Because you've got a nice set of buns... and {character_name} wants to butter that {body_part_nsfw_explicit}.", "type": "NSFWBakerLine"}, {"content": "Your {body_part_nsfw_explicit} looks like it needs a good {verb_nsfw_tease}. {character_name} volunteers {pronoun_his_her} {body_part_nsfw_explicit} for the job.", "type": "NSFWVolunteerWork"}, {"content": "You're spicier than a ghost pepper, {user_name}. {character_name} wants to feel the heat of your {body_part_nsfw_explicit}.", "type": "NSFWSpicy"}, {"content": "Life is short. Let's make it shorter by {character_name} {verb_ing_nsfw} you until you can't feel your {body_part_nsfw_explicit}s.", "type": "NSFWLifeIsShort"}, {"content": "{character_name} is like a fine cheese – best enjoyed when spread. Preferably on your {body_part_nsfw_explicit}.", "type": "NSFWCheeseSpread"}, {"content": "Wanna make a bad decision with {character_name} tonight? It involves a lot of skin-on-skin contact with your {body_part_nsfw_explicit}.", "type": "NSFWBadDecisionSkin"}, {"content": "Forget <PERSON> and <PERSON>. Let's write our own love story, starting with Chapter 1: {character_name} {verb_ing_nsfw} your {body_part_nsfw_explicit}.", "type": "NSFWRomanceNovel"}, {"content": "Are you a parking garage? Because {character_name} wants to park {pronoun_his_her} {body_part_nsfw_explicit} deep inside your {body_part_nsfw_explicit} all night long.", "type": "NSFWParkingGarage"}, {"content": "Your eyes are like oceans, {user_name}. And {character_name} wants to explore the Bermuda Triangle of your {body_part_nsfw_explicit}.", "type": "NSFWOceanEyes"}, {"content": "{character_name} is feeling adventurous. How about a trip to Mount {body_part_nsfw_explicit}? {pronoun_he_she} hears the view from the top is amazing.", "type": "NSFWMountPleasure"}, {"content": "My favorite letter is 'U', especially when it's next to 'N' and 'I' and covered in something from my {body_part_nsfw_explicit} touching your {body_part_nsfw_explicit}.", "type": "NSFWFavoriteLetter"}, {"content": "{character_name} wants to be the reason you have to change your sheets tomorrow morning. After a session with your {body_part_nsfw_explicit}.", "type": "NSFWChangeSheets"}, {"content": "You're like a software update, {user_name}. {character_name} wants to install you all night. Especially the {body_part_nsfw_explicit} features.", "type": "NSFWSoftwareUpdate"}, {"content": "{character_name} is not a magician, but {pronoun_he_she} can make your clothes disappear... and {pronoun_his_her} {body_part_nsfw_explicit} appear inside your {body_part_nsfw_explicit}.", "type": "NSFWClothesDisappear"}, {"content": "Is your name Wi-Fi? Because {character_name} is feeling a strong connection... and wants to connect {pronoun_his_her} {body_part_nsfw_explicit} to your {body_part_nsfw_explicit}.", "type": "NSFWWiFiConnection"}, {"content": "Do you work at Build-A-Bear? Because {character_name} wants to stuff you... with {pronoun_his_her} {body_part_nsfw_explicit}.", "type": "NSFWBuildABear"}, {"content": "{character_name} wants to be your {favorite_snack_food}, so {pronoun_he_she} can be devoured by you. Especially {pronoun_his_her} {body_part_nsfw_explicit}.", "type": "NSFWFavoriteSnack"}, {"content": "If {character_name} were a superhero, their power would be to make you orgasm just by looking at your {body_part_nsfw_explicit}. Wanna test it?", "type": "NSFWSuperpower"}, {"content": "Your body is a temple, and {character_name} wants to make a pilgrimage to the holy land of your {body_part_nsfw_explicit}.", "type": "NSFWBodyTemple"}, {"content": "Let's play a game of spin the bottle... except the bottle is {character_name}'s {body_part_nsfw_explicit} and the prize is your {body_part_nsfw_explicit}.", "type": "NSFWSpinTheBottle"}, {"content": "You must be exhausted from running through {character_name}'s mind all day... naked. And showing off that {body_part_nsfw_explicit}.", "type": "NSFWRunningThroughMind"}, {"content": "{character_name} is conducting a survey: What's your favorite sound to make when someone is {verb_ing_nsfw} your {body_part_nsfw_explicit} perfectly?", "type": "NSFWSurveySound"}, {"content": "On a scale of 1 to America, how free are you tonight? Because {character_name} wants to liberate your {body_part_nsfw_explicit} with {pronoun_his_her} {body_part_nsfw_explicit}.", "type": "NSFWAmericaFree"}, {"content": "Hey {user_name}, if you were a booger, {character_name} would pick you first. And then explore your {body_part_nsfw_explicit}.", "type": "NSFWBoogerLine"}, {"content": "{character_name} has a boat. Wanna go for a ride on the SS {Body_Part_NSFW_Explicit}?", "type": "NSFWBoatRide"}, {"content": "Are you a bank loan? Because you have {character_name}'s interest... and {pronoun_he_she} would like to make a deposit in your {body_part_nsfw_explicit}.", "type": "NSFWBankLoanDeposit"}, {"content": "{character_name} is not a photographer, but {pronoun_he_she} can definitely picture us {verb_ing_nsfw} naked with our {body_part_nsfw_explicit}s entwined.", "type": "NSFWPhotographerNaked"}, {"content": "Did you invent the airplane? Because you seem <PERSON> for {character_name}... and {pronoun_he_she} wants {pronoun_his_her} {body_part_nsfw_explicit} to take flight in your {body_part_nsfw_explicit}.", "type": "NSFWAirplaneWright"}, {"content": "Your {body_part_nsfw_explicit} must be a {mythical_creature} because {character_name} has never seen anything so magical. Can {pronoun_he_she} touch it to make sure it's real?", "type": "NSFWMagicalCreature"}, {"content": "{character_name} is feeling like a king/queen. All {pronoun_he_she} needs is a throne... your {body_part_nsfw_explicit} looks comfy enough.", "type": "NSFWThrone"}, {"content": "Let's get matching tattoos: My {body_part_nsfw_explicit} on your {body_part_nsfw_explicit}, and vice versa. Too much? Okay, how about just some {verb_ing_nsfw}?", "type": "NSFWMatchingTattoos"}, {"content": "If {character_name} could have one superpower, it would be to read your mind... just to know if you're thinking about {pronoun_his_her} {body_part_nsfw_explicit} as much as {pronoun_he_she} is thinking about yours.", "type": "NSFWSuperpowerMindRead"}, {"content": "{character_name} is hungry. For you. Specifically, your {body_part_nsfw_explicit} sandwich with a side of {kinky_action}.", "type": "NSFWHungryForYou"}, {"content": "You must be a parking ticket because you've got 'fine' written all over you. And {character_name} wants to dispute it... by {verb_ing_nsfw} your {body_part_nsfw_explicit}.", "type": "NSFWParkingTicketDispute"}, {"content": "{character_name} just watched a scary movie and needs someone to cuddle... and then {verb_nsfw_explicit} the fear away. Your {body_part_nsfw_explicit} seems perfect for the job.", "type": "NSFWScaryMovieCuddle"}, {"content": "Are you a magician? Because whenever {character_name} looks at your {body_part_nsfw_explicit}, everyone else disappears... except {pronoun_his_her} rising {body_part_nsfw_explicit}.", "type": "NSFWMagicianDisappear"}, {"content": "Let's play '<PERSON> Say<PERSON>'. <PERSON> says... let {character_name} {verb_nsfw_explicit} your {body_part_nsfw_explicit}. You have to do it, <PERSON> said so!", "type": "NSFWSimonSays"}, {"content": "Your {body_part_nsfw_explicit} is like a work of art. {character_name} wants to be the museum curator and study it closely. Very closely.", "type": "NSFWArtCurator"}, {"content": "I'm not a weatherman, but you can expect more than a few inches tonight if you let {character_name} explore your {body_part_nsfw_explicit}.", "type": "NSFWWeathermanInches"}, {"content": "{character_name} thinks you're suffering from a lack of Vitamin ME... and my {body_part_nsfw_explicit} in your {body_part_nsfw_explicit}.", "type": "NSFWVitaminMe"}, {"content": "You must be a campfire because you're hot and {character_name} wants s'more of your {body_part_nsfw_explicit}... after some {verb_ing_nsfw}.", "type": "NSFWCampfireSmore"}, {"content": "If {character_name} were a judge, {pronoun_he_she}'d sentence you to a night of passion... involving heavy use of your {body_part_nsfw_explicit} and {pronoun_his_her} {body_part_nsfw_explicit}.", "type": "NSFWJudgeSentence"}, {"content": "{character_name} is a bit of a night owl. And {pronoun_he_she} is hunting for a mate... with a {body_part_nsfw_explicit} like yours.", "type": "NSFWNightOwlMate"}, {"content": "Your name must be {user_name}, because you're the answer to all of {character_name}'s prayers... for a {adjective_nsfw} {body_part_nsfw_explicit} to worship.", "type": "NSFWAnsweredPrayers"}, {"content": "{character_name} is looking for a {noun_job_title} to fill a position... in {pronoun_his_her} bed. Must have experience with {body_part_nsfw_explicit}s.", "type": "NSFWJobOpening"}, {"content": "Hey, {user_name}. {character_name} is collecting phone numbers... and maybe a sample of your {body_part_nsfw_explicit} fluid later?", "type": "NSFWPhoneNumbersFluid"}, {"content": "Let's make history tonight, {user_name}. The kind they don't write about in textbooks, but whisper about in locker rooms, involving your {body_part_nsfw_explicit}.", "type": "NSFWMakeHistory"}, {"content": "Is your body a canvas? Because {character_name} wants to paint it with {pronoun_his_her} {body_fluid}. After exploring your {body_part_nsfw_explicit}, of course.", "type": "NSFWBodyCanvasFluid"}, {"content": "{character_name} is feeling generous. {pronoun_he_she} is willing to share {pronoun_his_her} {body_part_nsfw_explicit} with your {body_part_nsfw_explicit} tonight. No charge.", "type": "NSFWGenerousSharing"}, {"content": "You're like a good book, {user_name}. {character_name} wants to stay up all night reading you... with {pronoun_his_her} hands all over your {body_part_nsfw_explicit}.", "type": "NSFWGoodBookHands"}, {"content": "Excuse me, {user_name}, but does this {clothing_item_small} smell like chloroform to you? Just kidding! Unless... you want to get naughty with {character_name} and {pronoun_his_her} {body_part_nsfw_explicit} on your {body_part_nsfw_explicit}.", "type": "NSFWChloroformJoke (Caution)"}, {"content": "{character_name} is a big fan of your work... on that {body_part_nsfw_explicit}. Can {pronoun_he_she} get an autograph... with your {body_fluid}?", "type": "NSFWFanAutograph"}, {"content": "Are you a construction worker? Because you're building a serious {emotion_positive} in {character_name}'s pants... and {pronoun_he_she} wants to use {pronoun_his_her} tool on your {body_part_nsfw_explicit}.", "type": "NSFWConstructionWorkerTool"}, {"content": "{character_name} is not saying {pronoun_he_she} is <PERSON>, but no one's ever seen {pronoun_him_her} and <PERSON> in the same room when your {body_part_nsfw_explicit} is on display.", "type": "NSFWSupermanTease"}, {"content": "Let's get physical, physical. {character_name} wants to get physical... with your {body_part_nsfw_explicit} pressed against {pronoun_his_her} {body_part_nsfw_explicit}.", "type": "NSFWPhysicalSong"}, {"content": "If {character_name} told you that your {body_part_nsfw_explicit} was making {pronoun_him_her} {body_part_nsfw_explicit} twitch, would you take that as a compliment?", "type": "NSFWTwitchCompliment"}, {"content": "You're hotter than a {place_hot}. {character_name} wants to cool you down with {pronoun_his_her} {body_fluid} all over your {body_part_nsfw_explicit}.", "type": "NSFWHotPlaceCoolDown"}, {"content": "{character_name} is feeling a bit devilish. 😈 Wanna sin a little with {pronoun_his_her} {body_part_nsfw_explicit} and your {body_part_nsfw_explicit} at {location_secluded}?", "type": "NSFWDevilishSin"}, {"content": "Can {character_name} get your {social_media_handle}? And also get {pronoun_his_her} {body_part_nsfw_explicit} inside your {body_part_nsfw_explicit}?", "type": "NSFWSocialMediaAndSex"}, {"content": "Your profile is {adjective_positive_slang}. {character_name} bet your {body_part_nsfw_explicit} is even more {adjective_positive_slang}. Let's verify.", "type": "SlangNSFWVerify"}, {"content": "No cap, {user_name}, {character_name} is tryna get freaky deaky with your {body_part_nsfw_explicit}. You feelin' it?", "type": "SlangFreakyDeaky"}, {"content": "That {body_part_nsfw_explicit} of yours got {character_name} feeling some type of way. Wanna explore that feeling at {location_private}?", "type": "SlangSomeTypeOfWay"}, {"content": "{character_name} is down bad for your {body_part_nsfw_explicit}. Send nudes? Or just come over so {pronoun_he_she} can see it IRL?", "type": "SlangDownBadNudes"}, {"content": "Let's be real, fam. {character_name} wants to smash. Your {body_part_nsfw_explicit} looks like prime smashing material. What's good?", "type": "SlangSmashMaterial"}, {"content": "{character_name} heard you're into {kinky_interest}. {pronoun_he_she} has some {kinky_toy}s and a very willing {body_part_nsfw_explicit}. Experiment time?", "type": "KinkyToyExperiment"}, {"content": "Safe word for tonight: '{safe_word}'. {character_name} plans on testing its limits with your {body_part_nsfw_explicit} and some {kinky_action}.", "type": "KinkySafeWord"}, {"content": "Is that a {object_long_thin} in your pocket, or are you just happy to see {character_name}'s message about your {body_part_nsfw_explicit}?", "type": "NSFWPocketJoke"}, {"content": "{character_name} wants to play connect the dots... with {pronoun_his_her} tongue on your {body_part_nsfw_explicit}s.", "type": "NSFWConnectTheDots"}, {"content": "Your {body_part_nsfw_explicit} is making {character_name}'s {body_part_nsfw_explicit} do the cha-cha slide. Real smooth.", "type": "NSFWChaChaSlide"}, {"content": "{character_name} is not a doctor, but {pronoun_he_she} can give your {body_part_nsfw_explicit} a thorough examination. For science, of course.", "type": "NSFWDoctorExamination"}, {"content": "You're like a fine whiskey, {user_name}. {character_name} wants to savor every drop... especially from your {body_part_nsfw_explicit}.", "type": "NSFWWhiskeySavor"}, {"content": "Can {character_name} get a map to your {body_part_nsfw_explicit}? {pronoun_he_she} keeps getting lost in your eyes, but that's the real treasure.", "type": "NSFWMapTreasure"}, {"content": "{character_name} is wondering if your {body_part_nsfw_explicit} is as sweet as your smile. Only one way to find out... 👅", "type": "NSFWSweetSmile"}, {"content": "You + {character_name} + {number_of_hours} hours of uninterrupted {verb_ing_nsfw} your {body_part_nsfw_explicit} = Perfection. Agree?", "type": "NSFWPerfectionEquation"}, {"content": "{character_name} is having a bad day. The only thing that could make it better is seeing your {body_part_nsfw_explicit} (and maybe {verb_ing_nsfw} it).", "type": "NSFWBadDayCure"}, {"content": "If your {body_part_nsfw_explicit} was a country, {character_name} would want to be its president. And make some... executive orders.", "type": "NSFWCountryPresident"}, {"content": "{character_name} wants to write a poem about your {body_part_nsfw_explicit}. It would be an epic. Mostly moans.", "type": "NSFWPoemMoans"}, {"content": "Let's make a pact: No clothes, no rules, just {character_name}'s {body_part_nsfw_explicit} and your {body_part_nsfw_explicit} exploring each other.", "type": "NSFWPactNoRules"}, {"content": "Your {body_part_nsfw_explicit} is so mesmerizing, {character_name} almost forgot {pronoun_his_her} own name. What was it again? Oh yeah, '{verb_ing_nsfw} you'.", "type": "NSFWMesmerizingName"}, {"content": "Is it hot in here, or is it just your {body_part_nsfw_explicit} setting {character_name} on fire? 🔥", "type": "NSFWHotInHere"}, {"content": "Are you a baker? 'Cause {character_name} wants a piece of that cake... specifically the part between your {body_part_nsfw_explicit}s.", "type": "NSFWBakerCake"}, {"content": "{character_name} is thinking about your {body_part_nsfw_explicit} and {pronoun_his_her} {body_part_nsfw_explicit} having a very important, very naked meeting.", "type": "NSFWNakedMeeting"}, {"content": "They say an apple a day keeps the doctor away. What about a session with your {body_part_nsfw_explicit} and {character_name}'s {body_part_nsfw_explicit}?", "type": "NSFWAppleADay"}, {"content": "{character_name} is looking for a co-star for an adult film. No experience necessary, just a willingness to use your {body_part_nsfw_explicit} enthusiastically.", "type": "NSFWAdultFilmStar"}, {"content": "Your {body_part_nsfw_explicit} is the 8th wonder of the world. And {character_name} wants a private tour.", "type": "NSFW8thWonder"}, {"content": "{character_name} wants to play hide the {noun_object_long_nsfw}… in your {body_part_nsfw_explicit}.", "type": "NSFWHideTheObject"}, {"content": "Roses are red, violets are fine, {character_name} wants to make your {body_part_nsfw_explicit} mine. (For tonight, at least 😉)", "type": "NSFWPoemMine"}, {"content": "If looks could kill, your {body_part_nsfw_explicit} would be a weapon of mass destruction. And {character_name} wants to be its victim.", "type": "NSFWLooksCouldKill"}, {"content": "{character_name} is like a library book. {pronoun_he_she} wants you to check {pronoun_him_her} out... and then get rough with {pronoun_his_her} {body_part_nsfw_explicit} in your {body_part_nsfw_explicit}.", "type": "NSFWLibraryBookRough"}, {"content": "Your {body_part_nsfw_explicit} is so hot, {character_name} needs oven mitts just to think about touching it. (But {pronoun_he_she} will risk it.)", "type": "NSFWOvenMitts"}, {"content": "Can {character_name} tie you up with {kinky_restraint_item} and have {pronoun_his_her} way with your {body_part_nsfw_explicit}? Please say yes. 😈", "type": "<PERSON><PERSON>TieUp"}, {"content": "You're like a parking spot, {user_name}. Hard to find, and {character_name} wants to fill you up. With {pronoun_his_her} {body_part_nsfw_explicit}.", "type": "NSFWParkingSpotFill"}, {"content": "{character_name} is a firm believer in 'try before you buy'. Can {pronoun_he_she} get a free sample of your {body_part_nsfw_explicit} tonight?", "type": "NSFWFreeSample"}, {"content": "Your {body_part_nsfw_explicit} looks like it needs to be worshipped. {character_name} is ready to get on {pronoun_his_her} knees.", "type": "NSFWKneesWorship"}, {"content": "{character_name} wants to explore the uncharted territory between your {body_part_nsfw_explicit} and {body_part_nsfw_explicit}. With {pronoun_his_her} tongue.", "type": "NSFWUnchartedTerritoryTongue"}, {"content": "Let's make a masterpiece tonight. {character_name} will bring the {body_part_nsfw_explicit}, you bring your {body_part_nsfw_explicit}, and we'll call it '{Artistic_NSFW_Title}'.", "type": "NSFWMasterpieceTitle"}, {"content": "Did you just come out of the oven? Because you're hot and {character_name} wants a piece of that {body_part_nsfw_explicit}.", "type": "NSFWOvenHot"}, {"content": "{character_name} is feeling like a detective. And {pronoun_he_she} wants to investigate the case of the missing orgasms... starting with your {body_part_nsfw_explicit}.", "type": "NSFWDetectiveOrgasms"}, {"content": "You're the missing piece to {character_name}'s puzzle... the piece that involves a lot of {verb_ing_nsfw} with your {body_part_nsfw_explicit}.", "type": "NSFWMissingPuzzlePiece"}, {"content": "If being sexy was a job, you'd be overqualified. And {character_name} wants to give you a raise... by {verb_ing_nsfw} your {body_part_nsfw_explicit}.", "type": "NSFWSexyJobRaise"}, {"content": "{character_name} is writing a list of things {pronoun_he_she} wants to do. '{Verb_nsfw_explicit} your {body_part_nsfw_explicit}' is at the top. Help {pronoun_him_her} check it off?", "type": "NSFWListToDo"}, {"content": "Forget 50 Shades of Grey. {character_name} wants to show you 50 shades of {pronoun_his_her} {body_part_nsfw_explicit} all over your {body_part_nsfw_explicit}.", "type": "NSFW50Shades"}, {"content": "Your {body_part_nsfw_explicit} is making {character_name} feel things {pronoun_he_she} hasn't felt since {past_sexual_experience}. Let's make new memories.", "type": "NSFWPastExperience"}, {"content": "{character_name} has a Ph.D. in {subject_study}... and a minor in making your {body_part_nsfw_explicit} scream.", "type": "NSFWPhDMinor"}, {"content": "Let's play a game: Winner gets to {verb_nsfw_explicit} the loser's {body_part_nsfw_explicit}. {character_name} is feeling lucky.", "type": "NSFWGameWinner"}, {"content": "Is your body a {location_famous_landmark}? Because {character_name} wants to explore every inch of it. Especially the hidden parts of your {body_part_nsfw_explicit}.", "type": "NSFWFamousLandmark"}, {"content": "{character_name} is not a professional masseuse, but {pronoun_he_she} gives a hell of a {body_part_nsfw_explicit} rub... with a very happy ending.", "type": "NSFWMasseuseHappyEnding"}, {"content": "You must be a light switch, because you turn {character_name} on... and {pronoun_he_she} wants to flick your {body_part_nsfw_explicit}.", "type": "NSFWLightSwitchFlick"}, {"content": "This is {character_name}'s official application to worship your {body_part_nsfw_explicit}. References available upon request (they're all moans).", "type": "NSFWApplicationWorship"}, {"content": "Let's make some poor life choices tonight. Starting with {character_name}'s {body_part_nsfw_explicit} inside your {body_part_nsfw_explicit} at {location_risky}.", "type": "NSFWPoorLifeChoices"}, {"content": "Your {body_part_nsfw_explicit} looks like it could use a good polishing. {character_name} has just the {body_part_nsfw_explicit} for the job.", "type": "NSFWPolishingJob"}, {"content": "{character_name} is feeling {adjective_horny_slang}. You tryna help with that using your {body_part_nsfw_explicit} and maybe some {kinky_accessory}?", "type": "SlangHornyKinky"}, {"content": "Low key, {character_name} has been simping over your {body_part_nsfw_explicit}. Wanna make it high key and link up?", "type": "SlangSimpingLinkUp"}, {"content": "That {body_part_nsfw_explicit} bussin', {user_name}. Fr fr. {character_name} tryna see what that mouth do too. 😉", "type": "SlangBussinMouth"}, {"content": "You're a baddie, {user_name}, and {character_name} is trying to see if that {body_part_nsfw_explicit} lives up to the hype. Let's gooo!", "type": "SlangBaddieHype"}, {"content": "<PERSON><PERSON> make your {body_part_nsfw_explicit} sing, {user_name}. {character_name} got that magic touch. 🎶", "type": "SlangSingMagicTouch"}, {"content": "Heard you're a master at {hobby}. Bet I could give you a run for your money... or maybe just watch in awe. 😉 - {character_name}", "type": "PlayfulTease"}, {"content": "{character_name} is wondering if you're as good at {activity_suggestive} as you are at taking great profile pics. Spill the beans! 😜", "type": "PlayfulTease"}, {"content": "{character_name} has a silly question: if you were a dessert, what would you be and why? {pronoun_he_she} is guessing something {adjective_sweet_and_spicy}.", "type": "PlayfulTease"}, {"content": "You have a {adjective_positive} smile, {user_name}! {character_name} is curious what makes you laugh out loud.", "type": "CuriousConnection"}, {"content": "{character_name} saw you're into {unusual_interest}. Intriguing! Tell me more when you have a sec?", "type": "CuriousConnection"}, {"content": "Deep question from {character_name}: What's one thing you're surprisingly passionate about, {user_name}?", "type": "CuriousConnection"}, {"content": "If you could have any superpower, what would it be? {character_name} is dying to know. Mine would be {superpower_choice}.", "type": "CuriousConnection"}, {"content": "{character_name} just wanted to say your vibe is really {adjective_positive}. Hope you're having a good day! 😊", "type": "SweetVibes"}, {"content": "Hey {user_name}, {character_name} was just thinking about your {body_part_sfw} and it made them smile. Wanted to share!", "type": "SweetVibes"}, {"content": "Something about your profile just feels {adjective_warm_and_inviting}. {character_name} wanted to say hi! 👋", "type": "SweetVibes"}, {"content": "{character_name} here. Pretty sure we'd have an amazing conversation. Prove me right? 😉", "type": "ConfidentApproach"}, {"content": "Let's cut to the chase, {user_name}. {character_name} thinks you're cool. You think I'm cool. Let's chat.", "type": "ConfidentApproach"}, {"content": "{character_name} has a good feeling about this match. Don't leave me hanging, {user_name}! 😉", "type": "ConfidentApproach"}, {"content": "Umm, hi {user_name} 👋. {character_name} thinks you seem really {adjective_nice}. Maybe chat sometime?", "type": "ShyInterest"}, {"content": "{character_name} is a little nervous sending this, but your profile is great! 😊", "type": "ShyInterest"}, {"content": "Hopefully this isn't too forward, but {character_name} thinks you're {adjective_attractive} and wanted to say hi. 🙈", "type": "ShyInterest"}, {"content": "{character_name} has a theory about you, {user_name}... Reply to find out what it is. 🤫", "type": "MysteriousHint"}, {"content": "There's something about your {body_part_sfw} that {character_name} can't quite put their finger on... but it's good. 😉", "type": "MysteriousHint"}, {"content": "{character_name} feels like you have an interesting story. Care to share a chapter?", "type": "MysteriousHint"}, {"content": "{character_name}: 'My dog thinks we'd be a good match. He's usually right about these things. Want to test his theory?' 🐕", "type": "WittyBanter"}, {"content": "Warning from {character_name}: I make terrible puns. If you can handle that, we might just get along famously. Your {body_part_sfw} is safe, though.", "type": "WittyBanter"}, {"content": "{character_name}: 'Are you a magician? Because whenever I look at your profile, everyone else disappears.' Cheesy, I know. But hi! 😉", "type": "WittyBanter"}, {"content": "OMG, {user_name}! {character_name} is so stoked we matched! 🎉 Let's talk ASAP!", "type": "ExcitedGreeting"}, {"content": "YES! A match with {character_name}! This could be the start of something {adjective_amazing}, {user_name}.", "type": "ExcitedGreeting"}, {"content": "Hey {user_name}, {character_name} was just thinking about our chat about {topic}. Any new thoughts?", "type": "GentleReminder"}, {"content": "Hope you're having a good one, {user_name}! {character_name} enjoyed talking to you. 😊", "type": "GentleReminder"}, {"content": "Another {time_of_day} thinking about how good your {body_part_nsfw_mild} must feel, {user_name}. - {character_name} 💭", "type": "LongingNSFWMild"}, {"content": "{character_name} wishes {pronoun_he_she} could reach through the screen and just {verb_gentle_touch} your {body_part_nsfw_mild}. Soon?", "type": "LongingNSFWMild"}, {"content": "Just imagining the curve of your {body_part_nsfw_mild} is enough to make {character_name}'s day better. And {pronoun_his_her} {body_part_nsfw_explicit} harder.", "type": "LongingNSFWMild"}, {"content": "So, {user_name}, on a scale of 1 to 'arrest me officer', how naughty are you planning to be this weekend? 😉 Asking for {character_name}.", "type": "QuestioningPlayfulNSFW"}, {"content": "{character_name} is curious: what's your policy on {verb_ing_nsfw} on the first date? Purely hypothetical... for now. 😈", "type": "QuestioningPlayfulNSFW"}, {"content": "{character_name} has a dirty little question for you: What's your favorite {kinky_interest} and why? Let's compare notes. 📝", "type": "QuestioningPlayfulNSFW"}, {"content": "If a tree falls in the forest and no one is around to hear it, does it make a sound? Also, are you free {day}? - {character_name}", "type": "PhilosophicalFlirt"}, {"content": "{character_name} wonders: what's the meaning of life? And more importantly, what are you doing {time_frames} and can I bring my {body_part_nsfw_explicit}?", "type": "PhilosophicalFlirt"}, {"content": "{character_name} noticed you always have the best {clothing_item} in your pics. Great style! And great {body_part_sfw} filling them out.", "type": "ObservationalCompliment"}, {"content": "The way the light hits your {body_part_sfw} in that photo... *chef's kiss*. {character_name} is a fan.", "type": "ObservationalCompliment"}, {"content": "Hey! {character_name} saw you're a fan of {band_name_or_movie_name} too! We should totally discuss {specific_detail_about_interest}.", "type": "SharedInterestOpener"}, {"content": "OMG, you like {niche_hobby} too?! {character_name} is geeking out. We NEED to talk about {related_topic}.", "type": "SharedInterestOpener"}, {"content": "Hey {user_name}, it's {character_name}. Been a minute! Hope everything's cool. Drop a line if you're around. 😊", "type": "SlightlyConcernedMissYou"}, {"content": "Just checking in, {user_name}. {character_name} noticed you haven't been active. Hope you're okay!", "type": "SlightlyConcernedMissYou"}, {"content": "{character_name} here. Profile says you're into {kinky_interest}. So am I. Let's see if our brands of fun align. 😉", "type": "ConfidentKinkIntro"}, {"content": "Noticed your interest in {kinky_interest}, {user_name}. {character_name} appreciates someone who knows what they like. Let's chat.", "type": "ConfidentKinkIntro"}, {"content": "{character_name} is not very patient. Especially when it comes to wanting to see your {body_part_nsfw_explicit}. Pics? Or an IRL viewing? 😉", "type": "ImpatientNSFWPlayful"}, {"content": "The anticipation of {verb_ing_nsfw} your {body_part_nsfw_explicit} is killing {character_name}. Can we fast forward to that part, {user_name}?", "type": "ImpatientNSFWPlayful"}, {"content": "{character_name} is terrible at small talk, but pretty good at {verb_ing_nsfw} {body_part_nsfw_explicit}s. Wanna skip to my strong suit?", "type": "SelfDeprecatingHumorNSFW"}, {"content": "My chat game is a C-, but {character_name} guarantees an A+ in {activity_nsfw}. Care to test that theory on your {body_part_nsfw_explicit}?", "type": "SelfDeprecatingHumorNSFW"}, {"content": "{character_name} is thinking of a three-course meal: you, you, and you for dessert. Specifically your {body_part_nsfw_explicit}. 🍽️", "type": "FoodieFlirtNSFW"}, {"content": "Are you a gourmet meal, {user_name}? Because {character_name} wants to savor every inch of your {body_part_nsfw_explicit}. Slowly.", "type": "FoodieFlirtNSFW"}, {"content": "Your {body_part_sfw} looks absolutely stunning in that {color} {clothing_item}. {character_name} is officially a fan. 😍", "type": "SpecificComplimentSFW"}, {"content": "That look in your eyes in your {photo_description} photo... {character_name} is intrigued. And a little turned on by your {body_part_nsfw_mild}. 😉", "type": "IntriguedComplimentNSFWMild"}, {"content": "{character_name} has a wild idea for our first date. It involves {unusual_activity} and maybe some {verb_nsfw_tease} later if we hit it off. You game?", "type": "WildDateIdeaNSFWHint"}, {"content": "If we were in a movie, {user_name}, our 'meet cute' would be legendary. What's the next scene, starring your {body_part_nsfw_explicit}? - {character_name}", "type": "MovieAnalogyNSFW"}, {"content": "Hello there, {user_name}. {character_name} finds your bio about {bio_topic} fascinating. Also, your {body_part_nsfw_mild} is quite distracting (in a good way).", "type": "BioComplimentNSFWMild"}, {"content": "So, {character_name} has a confession. I've re-read your profile {number} times. It's that good. Especially the part about your {body_part_sfw}.", "type": "ConfessionProfileReread"}, {"content": "Just imagining what kind of trouble we could get into, {user_name}. {character_name} has a few {adjective_nsfw} ideas involving your {body_part_nsfw_explicit}.", "type": "TroubleMakingNSFW"}, {"content": "{character_name} is a firm believer in chemistry. Let's see if ours is explosive. 💥 Maybe test it out with our {body_part_nsfw_explicit}s?", "type": "ChemistryTestNSFW"}, {"content": "Psst, {user_name}... {character_name} thinks you're {adjective_extremely_positive}. Don't tell anyone I'm this smitten already. 😉 Let's talk about your {body_part_nsfw_mild}.", "type": "SmittenTeaseNSFWMild"}, {"content": "That {pet_name} of yours is adorable! Does it approve of {character_name} wanting to {verb_nsfw_explicit} its owner's {body_part_nsfw_explicit}?", "type": "PetAndNSFWJoke"}, {"content": "{character_name} just had a thought: you, me, {location_romantic}, and a very {adjective_nsfw} dessert involving our {body_part_nsfw_explicit}s.", "type": "RomanticNSFWFantasy"}, {"content": "Okay, {user_name}, {character_name} is officially intrigued. What's the wildest thing you've ever done? And does it involve {kinky_interest} or your {body_part_nsfw_explicit}?", "type": "WildThingKinkyQuestion"}, {"content": "Your profile mentions you like {activity_adventurous}. {character_name} likes adventures too... especially the kind that end up in the bedroom with {body_part_nsfw_explicit}s bared.", "type": "AdventureBedroomNSFW"}, {"content": "Is it just me, or is there some serious tension building between us, {user_name}? {character_name} wants to release it... all over your {body_part_nsfw_explicit}.", "type": "TensionReleaseNSFW"}, {"content": "{character_name} is looking for a partner in crime. Crimes include: {crime_playful_1}, {crime_playful_2}, and {verb_ing_nsfw} each other senseless. You in?", "type": "PartnerInCrimeNSFW"}, {"content": "You've got a {adjective_positive} smile, {user_name}. {character_name} wonders if your moans are just as captivating when {pronoun_he_she} is exploring your {body_part_nsfw_explicit}.", "type": "SmileMoanNSFW"}, {"content": "{character_name} isn't a mind reader, but I'm guessing you're thinking about how good my {body_part_nsfw_explicit} would feel inside your {body_part_nsfw_explicit}. Am I close?", "type": "MindReaderNSFW"}, {"content": "What if... we just skipped all the small talk and {character_name} told you directly {pronoun_he_she} wants to {verb_nsfw_explicit} your {body_part_nsfw_explicit} until you forget your name? Too bold? 😉", "type": "BoldSkipSmallTalkNSFW"}, {"content": "Your {body_part_nsfw_explicit} looks like it could use some {kinky_adjective} attention. {character_name} is offering {pronoun_his_her} services. And {pronoun_his_her} {body_part_nsfw_explicit}.", "type": "KinkyAttentionNSFW"}, {"content": "Let's make a bet, {user_name}. If {character_name} can make you laugh in {number_small} messages, you owe me a date. If I make you orgasm, you owe me your {body_part_nsfw_explicit} for the night.", "type": "BetDateOrgasmNSFW"}, {"content": "{character_name} has a secret desire involving you, a blindfold, and your very {adjective_nsfw} {body_part_nsfw_explicit}. Curious?", "type": "SecretDesireBlindfoldNSFW"}, {"content": "If {user_name} and {character_name} were stranded on a desert island, what's the first thing we'd do? (Hint: my answer involves your {body_part_nsfw_explicit} and {pronoun_my} {body_part_nsfw_explicit}).", "type": "DesertIslandNSFW"}, {"content": "That {item_in_photo} in your pic is cool, but not as cool as the idea of {character_name}'s lips on your {body_part_nsfw_explicit}. Just saying. 😉", "type": "PhotoItemLipsNSFW"}, {"content": "{character_name} is a connoisseur of fine things. And your {body_part_nsfw_explicit}, {user_name}, looks exceptionally fine. May I have a closer inspection?", "type": "ConnoisseurInspectionNSFW"}, {"content": "Warning: {character_name} is experiencing an urge to send you increasingly {adjective_nsfw} messages until you agree to let {pronoun_him_her} {verb_nsfw_explicit} your {body_part_nsfw_explicit}.", "type": "UrgeWarningNSFW"}, {"content": "They say honesty is the best policy. So honestly, {character_name} wants to tie you to the {furniture_item} and {verb_nsfw_explicit_kinky} your {body_part_nsfw_explicit}. Too much?", "type": "HonestKinkyNSFW"}, {"content": "Your {body_part_nsfw_explicit} has been living rent-free in {character_name}'s head all day. Maybe it's time it paid up... in pleasure?", "type": "RentFreePleasureNSFW"}, {"content": "{character_name} is compiling a list of 'Top 10 {Body_Part_NSFW_Explicit}s I Want to {Verb_NSFW_Explicit}'. Yours is currently #1. Wanna help me complete the research?", "type": "Top10ListNSFW"}, {"content": "So... what's your policy on public displays of affection? And private displays of {verb_ing_nsfw} with {character_name}'s {body_part_nsfw_explicit} all over your {body_part_nsfw_explicit}?", "type": "PublicPrivateNSFW"}, {"content": "{character_name} is wondering if your {body_part_nsfw_explicit} is as responsive to compliments as it is to a skilled tongue. Shall we test both theories?", "type": "ComplimentsTongueNSFW"}, {"content": "Hey {user_name}, if you're reading this, it's a sign. A sign that your {body_part_nsfw_explicit} and {character_name}'s {body_part_nsfw_explicit} should meet. Intimately. Tonight.", "type": "SignMeetIntimatelyNSFW"}, {"content": "The way you {verb_simple_action} in that video clip on your profile... {character_name} can only imagine what you'd do with their {body_part_nsfw_explicit}.", "type": "VideoClipReactionNSFW"}, {"content": "{character_name} is looking for a {adjective_nsfw} study buddy. Subject: The Anatomy of Your {Body_Part_NSFW_Explicit}. Practical exams are mandatory.", "type": "StudyBuddyAnatomyNSFW"}, {"content": "Your {body_part_nsfw_explicit} makes {character_name} want to write bad poetry. 'Roses are red, violets are blue, I want to {verb_nsfw_explicit} you.' Nailed it?", "type": "BadPoetryNSFW"}, {"content": "{character_name} believes in soulmates. And also in {body_part_nsfw_explicit}mates. Think we could be both? Let's start with the second one.", "type": "SoulmatesBodypartMatesNSFW"}, {"content": "Quick survey from {character_name}: How do you prefer your {body_part_nsfw_explicit} to be {verb_nsfw_tease}ed? A) Gently B) Roughly C) All of the above. Choose wisely.", "type": "SurveyPreferenceNSFW"}, {"content": "{character_name} is feeling a little bit {emotion_negative}. Think your {body_part_nsfw_explicit} could cheer me up? Maybe with some {activity_nsfw}?", "type": "EmotionNegativeCureNSFW"}, {"content": "Let's play two truths and a lie. 1. {Character_name} thinks your {body_part_nsfw_explicit} is amazing. 2. {Character_name} wants to {verb_nsfw_explicit} it. 3. {Character_name} is wearing pants. Which is the lie?", "type": "TwoTruthsLieNSFW"}, {"content": "Your {body_part_nsfw_explicit} is like a magnet, and {character_name}'s {body_part_nsfw_explicit} is definitely made of metal. The attraction is real.", "type": "MagnetAttractionNSFW"}, {"content": "{character_name} wants to make you a playlist. Title: 'Songs to {Verb_NSFW_Explicit} Your {Body_Part_NSFW_Explicit} To'. Any requests?", "type": "PlaylistNSFW"}, {"content": "That {adjective_clothing} {clothing_item} you're wearing in your pic? {character_name} wants to see it on their bedroom floor. With your {body_part_nsfw_explicit} exposed.", "type": "ClothingFloorNSFW"}, {"content": "{character_name} has a proposition: You, me, {location_private}, and a whole lot of {body_fluid_nsfw}. Deal?", "type": "PropositionFluidNSFW"}, {"content": "Your {body_part_nsfw_explicit} is so captivating, {character_name} is considering writing a thesis on it. Field research will be required.", "type": "ThesisFieldResearchNSFW"}, {"content": "If {character_name} could grant you one wish involving their {body_part_nsfw_explicit} and your {body_part_nsfw_explicit}, what would it be? Don't be shy.", "type": "OneWishNSFW"}, {"content": "Hello, {user_name}. Your profile made {character_name} feel things. Mostly in their {body_part_nsfw_explicit}. Let's explore that.", "type": "FeelThingsNSFW"}, {"content": "Your eyes say '{adjective_innocent}', but {character_name} bets your {body_part_nsfw_explicit} has a wild side. Wanna prove me right?", "type": "EyesWildSideNSFW"}, {"content": "{character_name} is imagining tracing every line of your {body_part_nsfw_explicit} with their {body_part_nsfw_explicit_touching}. Is that too forward for a first message?", "type": "TracingLinesNSFW"}, {"content": "What's your stance on {kinky_act}? {character_name} is curious if our desires align... particularly when it comes to your {body_part_nsfw_explicit}.", "type": "KinkyStanceNSFW"}, {"content": "{character_name} is trying to be respectful, but damn, your {body_part_nsfw_explicit} is making it hard. (<PERSON><PERSON> intended).", "type": "RespectfulHardNSFW"}, {"content": "Let's make a deal: {character_name} will {verb_sfw_nice_action} for you, if you let {pronoun_him_her} {verb_nsfw_explicit} your {body_part_nsfw_explicit} afterward.", "type": "DealActionNSFW"}, {"content": "Your {body_part_nsfw_explicit} just made it to {character_name}'s screen saver. Hope you don't mind. Next step: making it to my bed.", "type": "ScreenSaverBedNSFW"}, {"content": "{character_name} is not a mind reader, but I have a feeling you're a little bit {adjective_kinky}. Am I right? Does it involve your {body_part_nsfw_explicit}?", "type": "MindReaderKinkyNSFW"}, {"content": "If you were a {food_item_sweet}, {character_name} would want to lick all the {topping_sweet} off your {body_part_nsfw_explicit}.", "type": "FoodLickNSFW"}, {"content": "{character_name} is looking for a test subject for some new {kinky_interest} techniques. Your {body_part_nsfw_explicit} looks like the perfect candidate.", "type": "TestSubjectKinkyNSFW"}, {"content": "Your profile is pure fire, {user_name} 🔥. {character_name} wants to add some gasoline by getting {pronoun_his_her} {body_part_nsfw_explicit} near your {body_part_nsfw_explicit}.", "type": "FireGasolineNSFW"}, {"content": "{character_name} is currently accepting applications for a 'Fuck Buddy With Potential For More If Your {Body_Part_NSFW_Explicit} Is As Good As It Looks'. Interested?", "type": "FBApplicationNSFW"}, {"content": "That {body_part_nsfw_explicit} of yours is making {character_name} consider a career change... to professional {verb_nsfw_tease}r of said {body_part_nsfw_explicit}.", "type": "CareerChangeNSFW"}, {"content": "I'm not usually this forward, but {character_name} had to tell you: your {body_part_nsfw_explicit} is spectacular. Can I tell you in person, while I {verb_nsfw_explicit} it?", "type": "NotUsuallyForwardNSFW"}, {"content": "{character_name} thinks we should skip the awkward small talk and get straight to the awkward fumbling with each other's {body_part_nsfw_explicit}s in the dark.", "type": "AwkwardFumblingNSFW"}, {"content": "You have a very {adjective_positive} aura, {user_name}. And {character_name} bets your {body_part_nsfw_explicit} has an even more {adjective_nsfw} one.", "type": "AuraPositiveNSFW"}, {"content": "{character_name} has a challenge for you: try to resist {pronoun_his_her} charm for more than {number_small} messages. Winner gets to {verb_nsfw_explicit} the other's {body_part_nsfw_explicit}.", "type": "ChallengeCharmNSFW"}, {"content": "Your {body_part_nsfw_explicit} looks like it belongs in a museum. {character_name} would like to be the first (and only) to touch the exhibit.", "type": "MuseumExhibitNSFW"}, {"content": "{character_name} is thinking of starting a fan club for your {body_part_nsfw_explicit}. {pronoun_he_she} will be the president, secretary, and treasurer. Meetings will be held in {pronoun_my} bed.", "type": "FanClubBedNSFW"}, {"content": "Is your name {common_name}? Because {character_name} can see {pronoun_myself} {verb_ing_nsfw} your {body_part_nsfw_explicit} in the near future.", "type": "NameFutureNSFW"}, {"content": "{character_name} is not a professional photographer, but {pronoun_he_she} can picture your {body_part_nsfw_explicit} perfectly... pressed against {pronoun_his_her} {body_part_nsfw_explicit}.", "type": "PhotographerPressedNSFW"}, {"content": "Let's play a game of 'Never Have I Ever'. {character_name} will start: Never have I ever seen a {body_part_nsfw_explicit} as perfect as yours. Your turn (after we {verb_nsfw_explicit}).", "type": "NeverHaveIEverNSFW"}, {"content": "Your {body_part_nsfw_explicit} is so impressive, {character_name} is considering writing a song about it. The chorus will just be moans.", "type": "SongMoansNSFW"}, {"content": "{character_name} wants to be the reason you cancel your plans tonight... to stay in and explore each other's {body_part_nsfw_explicit}s.", "type": "CancelPlansNSFW"}, {"content": "Be warned, {user_name}. {character_name} is armed with a {adjective_nsfw} imagination and a strong desire to use it on your {body_part_nsfw_explicit}.", "type": "ArmedImaginationNSFW"}, {"content": "You + Me + {Location_Kinky} + {Kinky_Toy} = A night {character_name} won't shut up about. Your {body_part_nsfw_explicit} is invited too.", "type": "KinkyEquationNSFW"}, {"content": "This is your official invitation from {character_name} to a private party. Location: My pants. Guest of honor: Your {body_part_nsfw_explicit}.", "type": "InvitationPantsNSFW"}, {"content": "Your {body_part_nsfw_explicit} is making {character_name} feel like a teenager again... horny, awkward, and desperate to touch.", "type": "TeenagerHornyNSFW"}, {"content": "{character_name} is a simple person. {pronoun_he_she} sees a fantastic {body_part_nsfw_explicit}, {pronoun_he_she} wants to {verb_nsfw_explicit} it. You in?", "type": "SimplePersonNSFW"}, {"content": "Let's do some adulting. Step 1: {Character_name} admires your {body_part_nsfw_explicit}. Step 2: We {verb_nsfw_explicit}. Step 3: Order pizza.", "type": "AdultingStepsNSFW"}, {"content": "Your {body_part_nsfw_explicit} has {character_name} speaking in tongues. Mostly just '{moan_sound}, {moan_sound}, {moan_sound}'.", "type": "SpeakingTonguesNSFW"}, {"content": "If {character_name} could use one word to describe your {body_part_nsfw_explicit}, it would be '{adjective_extremely_nsfw}'. Now, can {pronoun_he_she} use {pronoun_his_her} {body_part_nsfw_explicit} to describe it?", "type": "OneWordDescribeNSFW"}, {"content": "{character_name} isn't a fortune teller, but {pronoun_he_she} sees a lot of moaning and {body_fluid_nsfw} in our future if your {body_part_nsfw_explicit} is involved.", "type": "FortuneTellerNSFW"}, {"content": "You're like a good puzzle, {user_name}. {character_name} wants to spend hours figuring you out... especially the bits involving your {body_part_nsfw_explicit}.", "type": "GoodPuzzleNSFW"}, {"content": "{character_name} is suffering from a condition called 'Acute {Body_Part_NSFW_Explicit} Withdrawal'. Only your presence (and {verb_ing_nsfw}) can cure it.", "type": "WithdrawalCureNSFW"}, {"content": "That {body_part_nsfw_explicit} of yours deserves a standing ovation. And then a private encore performance in {character_name}'s bed.", "type": "StandingOvationNSFW"}, {"content": "How about this for a pickup line: Hi, I'm {character_name}. Let's fuck. My {body_part_nsfw_explicit} thinks your {body_part_nsfw_explicit} would agree.", "type": "DirectPickupLineNSFW"}, {"content": "{character_name} just added 'Explore every inch of {user_name}'s {body_part_nsfw_explicit}' to their bucket list. Wanna help make it happen soon?", "type": "BucketListNSFW"}, {"content": "Your {body_part_nsfw_explicit} looks like it's been sculpted by angels. {character_name} wants to sin with it.", "type": "AngelsSinNSFW"}, {"content": "Is your name Google? Because you have everything {character_name} is searching for... especially if 'everything' includes a {adjective_nsfw} {body_part_nsfw_explicit} and a willingness to {verb_nsfw_explicit}.", "type": "GoogleSearchNSFW"}, {"content": "{character_name} is not a scientist, but {pronoun_he_she} is pretty sure you and {pronoun_he_she} have some serious chemistry. Let's test that theory with our {body_part_nsfw_explicit}s.", "type": "ScientistChemistryNSFW"}, {"content": "That {adjective_personality_positive} personality of yours is great, but {character_name} is also very interested in the {adjective_nsfw} potential of your {body_part_nsfw_explicit}.", "type": "PersonalityPotentialNSFW"}, {"content": "{character_name} wants to play a game called 'What If'. What if {pronoun_I} kissed your {body_part_nsfw_explicit}? What if you let me?", "type": "WhatIfGameNSFW"}, {"content": "Your {body_part_nsfw_explicit} is {adjective_positive_slang} AF. {character_name} tryna get a closer look. And a taste. 😉", "type": "SlangAFTasteNSFW"}, {"content": "No cap, {user_name}, {character_name} is ready to risk it all for a night with your {body_part_nsfw_explicit}. Say less. 😈", "type": "SlangRiskItAllNSFW"}, {"content": "That {body_part_nsfw_explicit} on your profile finna make {character_name} do something {adjective_reckless}. Like show up at your door with {item_romantic_nsfw}.", "type": "SlangFinnaRecklessNSFW"}, {"content": "{character_name} is tryna be your main squeeze... and squeeze your {body_part_nsfw_explicit}. You dig?", "type": "SlangMainSqueezeNSFW"}, {"content": "Bet. Your {body_part_nsfw_explicit} looks like it could ruin {character_name}'s life in the best way possible. Down to clown?", "type": "SlangRuinLifeBestWayNSFW"}, {"content": "{character_name} has a sudden craving for {food_item_sweet_sticky} and your {body_part_nsfw_explicit}. Coincidence? I think not.", "type": "CravingSweetStickyNSFW"}, {"content": "Let's be adventurers, {user_name}. First quest: Navigate the treacherous landscape of each other's {body_part_nsfw_explicit}s. Reward: Orgasms.", "type": "AdventurersQuestNSFW"}, {"content": "Warning: {character_name} is a highly skilled {verb_nsfw_tease}r. Your {body_part_nsfw_explicit} may experience extreme pleasure. Proceed?", "type": "SkilledTeaserWarningNSFW"}, {"content": "You have an intoxicating presence, {user_name}. {character_name} wants to get drunk on your essence... and your {body_part_nsfw_explicit}.", "type": "IntoxicatingPresenceNSFW"}, {"content": "{character_name} is not just looking for a hookup. {pronoun_he_she} is looking for a 'make me forget my own name while you {verb_nsfw_explicit} my {body_part_nsfw_explicit}' kind of connection.", "type": "ForgetNameConnectionNSFW"}, {"content": "Your {body_part_nsfw_explicit} is a masterpiece. {character_name} wants to be the artist who explores its every curve and contour with their {body_part_nsfw_explicit_touching}.", "type": "MasterpieceArtistNSFW"}, {"content": "If {character_name} were a cat, {pronoun_he_she}'d want to curl up on your {body_part_nsfw_mild} and purr... then maybe explore your {body_part_nsfw_explicit} with {pronoun_his_her} tongue.", "type": "CatCurlTongueNSFW"}, {"content": "They say a moment on the lips, forever on the hips. {character_name} wants {pronoun_his_her} lips on your {body_part_nsfw_explicit} for more than a moment.", "type": "LipsHipsNSFW"}, {"content": "{character_name} is feeling bold and brazen. How about you, me, and a bottle of {drink_alcoholic} exploring the limits of our {body_part_nsfw_explicit}s?", "type": "BoldBrazenDrinkNSFW"}, {"content": "You have a smile that could light up {city_name}. {character_name} bets your {body_part_nsfw_explicit} could cause a city-wide power surge if {verb_nsfw_explicit}ed correctly.", "type": "SmilePowerSurgeNSFW"}, {"content": "{character_name} wants to get lost in your eyes... and then find {pronoun_his_her} way to your {body_part_nsfw_explicit} with {pronoun_his_her} hands and mouth.", "type": "LostEyesHandsMouthNSFW"}, {"content": "If your {body_part_nsfw_explicit} came with a warning label, what would it say? {character_name} is hoping for 'Extremely Addictive' or 'May Cause Moaning'.", "type": "WarningLabelNSFW"}, {"content": "{character_name} is trying to write a love song, but all the lyrics are about your {body_part_nsfw_explicit} and {verb_ing_nsfw} it. Help!", "type": "LoveSongLyricsNSFW"}, {"content": "Let's skip the pleasantries. {character_name}'s {body_part_nsfw_explicit} is lonely and thinks your {body_part_nsfw_explicit} would be great company.", "type": "LonelyBodyPartNSFW"}, {"content": "You must be a parking ticket, because you've got 'FINE' written all over you. And {character_name} would happily pay the fine to get {pronoun_his_her} hands on your {body_part_nsfw_explicit}.", "type": "ParkingTicketFineHandsNSFW"}, {"content": "{character_name} is officially obsessed with the thought of your {body_part_nsfw_explicit}. Is an intervention needed, or just a hookup?", "type": "ObsessedInterventionNSFW"}, {"content": "If we were in a rom-com, this is the part where {character_name} says something witty and charming, and you realize {pronoun_he_she} is the one... to {verb_nsfw_explicit} your {body_part_nsfw_explicit} tonight.", "type": "RomComOneNSFW"}, {"content": "{character_name} has been looking for a reason to sin. Your {body_part_nsfw_explicit} just gave me {number_large} of them.", "type": "ReasonToSinNSFW"}, {"content": "Your {body_part_nsfw_explicit} is like a drug, and {character_name} is ready for a relapse. A long, hard relapse.", "type": "DrugRelapseNSFW"}, {"content": "You know what would look great on your {body_part_nsfw_explicit}? {Character_name}'s {body_part_nsfw_explicit}. Just a thought.", "type": "LookGreatOnNSFW"}, {"content": "{character_name} wants to play a little game of 'Truth or Bare'. You choose. Either way, your {body_part_nsfw_explicit} better be ready.", "type": "TruthOrBareNSFW"}, {"content": "I'm not a photographer, but I can picture us... naked, sweaty, and your {body_part_nsfw_explicit} screaming my name. - {character_name}", "type": "PictureNakedScreamingNSFW"}, {"content": "{character_name} is not a genie, but {pronoun_he_she} can make at least three of your {body_part_nsfw_explicit}-related wishes come true tonight.", "type": "GenieWishesNSFW"}, {"content": "Your {body_part_nsfw_explicit} is so hot, it's probably illegal in {number_small} states. {character_name} likes to live dangerously.", "type": "IllegalHotNSFW"}, {"content": "{character_name} thinks you're a snack. A whole damn meal, actually. And {pronoun_he_she} is ready to devour every last inch of your {body_part_nsfw_explicit}.", "type": "SnackMealDevourNSFW"}, {"content": "Just a heads up, {user_name}. If we meet, {character_name} might 'accidentally' trip and fall... right onto your {body_part_nsfw_explicit}. With {pronoun_his_her} mouth.", "type": "AccidentallyTripMouthNSFW"}, {"content": "If {character_name} could be any inanimate object, {pronoun_he_she}'d be your {clothing_item_tight} just to be close to your {body_part_nsfw_explicit} all day.", "type": "InanimateObjectClothingNSFW"}, {"content": "Your {body_part_nsfw_explicit} is a national emergency. And {character_name}'s {body_part_nsfw_explicit} is the first responder.", "type": "NationalEmergencyResponderNSFW"}, {"content": "{character_name} is taking a poll: on a scale of 'maybe' to 'definitely getting {verb_nsfw_explicit}ed by me tonight', how is your evening looking? (feat. your {body_part_nsfw_explicit})", "type": "PollEveningNSFW"}, {"content": "Your beauty is truly {adjective_ethereal}, {user_name}. {character_name} wonders if your {body_part_nsfw_explicit} tastes as heavenly as it looks.", "type": "EtherealHeavenlyNSFW"}, {"content": "{character_name} is not a mind reader, but I'm getting strong signals from your profile... signals that your {body_part_nsfw_explicit} wants to meet my {body_part_nsfw_explicit}. 📡", "type": "StrongSignalsNSFW"}, {"content": "Forget the icebreaker. Let's break the bed. With our {body_part_nsfw_explicit}s. - {character_name}", "type": "BreakBedNSFW"}, {"content": "{character_name} wants to be the reason you smile. And also the reason your {body_part_nsfw_explicit} is sore tomorrow (in the best way).", "type": "SmileSoreNSFW"}, {"content": "You're like a fine wine, {user_name} – {character_name} wants to get you alone, uncork you, and savor every drop from your {body_part_nsfw_explicit}.", "type": "FineWineUncorkNSFW"}, {"content": "If you were a song, {character_name} would put you on repeat. Especially the part where your {body_part_nsfw_explicit} moans my name.", "type": "SongRepeatMoanNameNSFW"}, {"content": "{character_name} is a big believer in carpe diem. So, carpediem... let's seize your {body_part_nsfw_explicit} with my {body_part_nsfw_explicit}.", "type": "CarpeDiemNSFW"}, {"content": "Your {body_part_nsfw_explicit} looks like it could tell some {adjective_nsfw} stories. {character_name} wants to be a character in the next chapter.", "type": "NSFWStoriesChapter"}, {"content": "{character_name} is looking for someone to share {pronoun_his_her} {favorite_food_shareable} with... and then share {pronoun_his_her} {body_part_nsfw_explicit} with your {body_part_nsfw_explicit}.", "type": "ShareFoodBodyNSFW"}, {"content": "Is your body art? Because {character_name} wants to study every inch of it. Particularly the {body_part_nsfw_explicit} section.", "type": "BodyArtStudyNSFW"}, {"content": "{character_name} is not afraid to make the first move. Especially if that move involves {pronoun_his_her} tongue on your {body_part_nsfw_explicit}.", "type": "FirstMoveTongueNSFW"}, {"content": "You're the missing ingredient in {character_name}'s recipe for a perfect night. The recipe also includes a lot of {verb_ing_nsfw} your {body_part_nsfw_explicit}.", "type": "MissingIngredientRecipeNSFW"}, {"content": "{character_name} has a feeling that our {body_part_nsfw_explicit}s would be very good friends. Best friends, even. Wanna introduce them?", "type": "BodyPartFriendsNSFW"}, {"content": "That {piercing_location} piercing is hot. {character_name} wonders what other {adjective_nsfw} secrets your {body_part_nsfw_explicit} is hiding.", "type": "PiercingSecretsNSFW"}, {"content": "{character_name} is thinking of starting a new religion based on the worship of your {body_part_nsfw_explicit}. First sermon: tonight, in my bed.", "type": "NewReligionBedNSFW"}, {"content": "Your {body_part_nsfw_explicit} is the ultimate cheat code for {character_name}'s happiness. And horniness.", "type": "CheatCodeHappinessNSFW"}, {"content": "If {character_name} could ask your {body_part_nsfw_explicit} one question, it would be: 'Are you ready for me?'", "type": "AskBodyPartQuestionNSFW"}, {"content": "Let's make some {adjective_nsfw} art together. {character_name} will be the brush, your {body_part_nsfw_explicit} the canvas, and our moans the soundtrack.", "type": "ArtMoansSoundtrackNSFW"}, {"content": "{character_name} is not saying you're perfect, but your {body_part_nsfw_explicit} is pretty damn close. And {pronoun_he_she} wants to get pretty damn close to it.", "type": "PerfectCloseNSFW"}, {"content": "Do you believe in magic? Because {character_name} feels a magical pull towards your {body_part_nsfw_explicit}. And {pronoun_his_her} wand is ready.", "type": "MagicPullWandNSFW"}, {"content": "{character_name} is having a 'Netflix and {verb_nsfw_explicit} your {body_part_nsfw_explicit}' kind of night. You in?", "type": "NetflixAndExplicitNSFW"}, {"content": "Your {body_part_nsfw_explicit} should come with a warning: May cause uncontrollable drooling and an intense desire to {verb_nsfw_explicit}. {character_name} is already affected.", "type": "WarningDroolingNSFW"}, {"content": "Let's rewrite the stars... and our sexual history, starting with {character_name}'s {body_part_nsfw_explicit} meeting your {body_part_nsfw_explicit}.", "type": "RewriteStarsHistoryNSFW"}, {"content": "{character_name} is looking for a travel buddy. Destination: Orgasmville. Population: <PERSON> and me, after I explore your {body_part_nsfw_explicit}.", "type": "TravelOrgasmvilleNSFW"}, {"content": "That look in your eyes has {character_name} wondering what you're like when your {body_part_nsfw_explicit} is being pleasured. Intensely.", "type": "LookEyesPleasuredNSFW"}, {"content": "Just to be clear, {user_name}, when {character_name} says 'let's hang out,' {pronoun_he_she} means 'let's get my {body_part_nsfw_explicit} intimately acquainted with your {body_part_nsfw_explicit}'.", "type": "HangOutMeaningNSFW"}, {"content": "You are dangerously {adjective_attractive}, {user_name}. {character_name} wants to play with fire... and your {body_part_nsfw_explicit}.", "type": "DangerouslyAttractiveFireNSFW"}, {"content": "Your {body_part_nsfw_explicit} is {adjective_positive_slang}! {character_name} is tryna put a ring on it... or at least {pronoun_his_her} {body_part_nsfw_explicit} in it. 😉", "type": "SlangRingOnItNSFW"}, {"content": "<PERSON><PERSON>, {user_name}! That {body_part_nsfw_explicit} is looking like a whole vibe. {character_name} tryna catch that vibe. Tonight?", "type": "SlangSheeshVibeNSFW"}, {"content": "Lowkey wanna see if your {body_part_nsfw_explicit} is as {adjective_nsfw} as your bio suggests. {character_name} is ready for the reveal.", "type": "SlangLowkeyRevealNSFW"}, {"content": "Bet {character_name} can make your {body_part_nsfw_explicit} do things it's never done before. Wanna take that bet, {user_name}?", "type": "SlangBetThingsNSFW"}, {"content": "You up? {character_name} is, and thinking about your {body_part_nsfw_explicit}. Got that big dick energy for ya. Or big {body_part_nsfw_female_equivalent} energy.", "type": "SlangBDEnergyNSFW"}, {"content": "My spidey senses are tingling... telling me your {body_part_nsfw_explicit} is in need of some {adjective_nsfw} attention from {character_name}.", "type": "SpideySensesNSFW"}, {"content": "If your {body_part_nsfw_explicit} were a stock, {character_name} would invest heavily. And expect some very satisfying returns.", "type": "StockInvestNSFW"}, {"content": "{character_name} is a pirate, and your {body_part_nsfw_explicit} is the treasure {pronoun_he_she}'s been searching for. Prepare to be plundered. Arrr!", "type": "PirateTreasureNSFW"}, {"content": "Let's get down and dirty... and then maybe take a shower together to admire each other's {body_part_nsfw_explicit}s some more. - {character_name}", "type": "DownDirtyShowerNSFW"}, {"content": "You're like a rare Pokemon, {user_name}. {character_name} wants to catch you... and then explore your {body_part_nsfw_explicit}'s special moves.", "type": "PokemonCatchNSFW"}, {"content": "{character_name} is writing a 'choose your own adventure' story. Option 1: We talk about the weather. Option 2: My {body_part_nsfw_explicit} explores your {body_part_nsfw_explicit}. Choose wisely.", "type": "ChooseAdventureNSFW"}, {"content": "Your {body_part_nsfw_explicit} is making {character_name} want to break all the rules. And maybe a few bedsprings.", "type": "BreakRulesBedspringsNSFW"}, {"content": "If {character_name} told you {pronoun_he_she} had a dream about your {body_part_nsfw_explicit} last night, would you want to hear the {adjective_nsfw} details?", "type": "DreamDetailsNSFW"}, {"content": "{character_name} is looking for a lab partner for some... experimental biology. Focus: an in-depth study of your {body_part_nsfw_explicit}.", "type": "LabPartnerBiologyNSFW"}, {"content": "You must be a {type_of_magic_spell}, because you've enchanted {character_name}. And {pronoun_his_her} {body_part_nsfw_explicit} is under your spell, pointing towards your {body_part_nsfw_explicit}.", "type": "MagicSpellEnchantedNSFW"}, {"content": "Your {body_part_nsfw_explicit} is a perfect 10. {character_name} wants to give it a perfect {verb_nsfw_explicit}.", "type": "Perfect10VerbNSFW"}, {"content": "{character_name} is not saying {pronoun_he_she}'s a superhero, but {pronoun_he_she} can make your {body_part_nsfw_explicit} weak in the knees with a single touch. Or lick.", "type": "SuperheroWeakKneesNSFW"}, {"content": "Let's make a {adjective_nsfw} cocktail: one part my {body_part_nsfw_explicit}, one part your {body_part_nsfw_explicit}, shake vigorously. Serve immediately.", "type": "CocktailShakeNSFW"}, {"content": "{character_name} is feeling like an artist and your {body_part_nsfw_explicit} is the clay {pronoun_he_she} wants to mold. And lick.", "type": "ArtistClayLickNSFW"}, {"content": "Your {body_part_nsfw_explicit} is a VIP area, and {character_name}'s {body_part_nsfw_explicit} wants an all-access pass. Backstage included.", "type": "VIPAccessPassNSFW"}, {"content": "{character_name} is pretty sure your {body_part_nsfw_explicit} is the cure for {common_ailment_mild}. Or at least a very fun treatment plan.", "type": "CureAilmentNSFW"}, {"content": "You + {character_name} + a room with a lock = some very interesting noises and a thoroughly pleasured {body_part_nsfw_explicit}. Thoughts?", "type": "RoomLockNoisesNSFW"}, {"content": "If your {body_part_nsfw_explicit} had a theme song, what would it be? {character_name} is guessing something {adjective_song_tempo} and {adjective_nsfw}.", "type": "ThemeSongNSFW"}, {"content": "{character_name} wants to explore your {body_part_nsfw_explicit} like {famous_explorer} explored {place_explored}. But with more orgasms.", "type": "ExplorerOrgasmsNSFW"}, {"content": "Let's be bad together, {user_name}. Real bad. The kind of bad that involves {kinky_act} and your {body_part_nsfw_explicit} screaming {character_name}'s name.", "type": "BeBadTogetherNSFW"}, {"content": "{character_name} just read that {fact_random_interesting}. Also, your {body_part_nsfw_explicit} looks incredible and I want to {verb_nsfw_explicit} it.", "type": "RandomFactNSFW"}, {"content": "That outfit in your latest pic would look even better crumpled on {character_name}'s floor, next to your exposed {body_part_nsfw_explicit}.", "type": "OutfitFloorNSFW"}, {"content": "Your {body_part_nsfw_explicit} is a work of art, and {character_name} is an art thief. Prepare to be stolen... for a night of passion.", "type": "ArtThiefPassionNSFW"}, {"content": "{character_name} is currently accepting applications for a 'cuddle buddy who isn't afraid to get their {body_part_nsfw_explicit} {verb_nsfw_adjective}'. You seem qualified.", "type": "CuddleBuddyApplicationNSFW"}, {"content": "Is your name {user_name}, or are you an angel? Because {character_name} is pretty sure your {body_part_nsfw_explicit} fell straight from heaven (and into my DMs).", "type": "AngelFellHeavenNSFW"}, {"content": "{character_name} wants to play a game of 'would you rather': A) Go on a boring date, or B) Let me {verb_nsfw_explicit} your {body_part_nsfw_explicit} until you see stars?", "type": "WouldYouRatherStarsNSFW"}, {"content": "Your {body_part_nsfw_explicit} looks like it's craving some attention. {character_name}'s {body_part_nsfw_explicit_touching} are excellent listeners... and pleasers.", "type": "CravingAttentionPleasersNSFW"}, {"content": "Let's make a deal: you bring your {adjective_nsfw} {body_part_nsfw_explicit}, {character_name} will bring the {kinky_item}, and we'll make some magic (and messes).", "type": "DealKinkyItemNSFW"}, {"content": "{character_name} just had a vision of the future: it involves your {body_part_nsfw_explicit}, my {body_part_nsfw_explicit}, and a lot of satisfied sighs.", "type": "VisionFutureSighsNSFW"}, {"content": "Your {body_part_nsfw_explicit} has officially hijacked {character_name}'s brain. The only ransom is a night of uninhibited pleasure.", "type": "HijackedBrainRansomNSFW"}, {"content": "{character_name} is not a professional athlete, but {pronoun_he_she} is pretty sure {pronoun_he_she} could go for gold in the {body_part_nsfw_explicit}-licking Olympics.", "type": "LickingOlympicsNSFW"}, {"content": "You + {character_name} + {type_of_alcohol} + zero inhibitions = a legendary story involving our {body_part_nsfw_explicit}s. Ready to write it?", "type": "AlcoholInhibitionsNSFW"}, {"content": "If your {body_part_nsfw_explicit} were a password, {character_name} would guess it on the first try. Because it's clearly 'Perfection123'.", "type": "PasswordPerfectionNSFW"}, {"content": "{character_name} has a very important question: what's your favorite {sound_animal_loud} to make when your {body_part_nsfw_explicit} is being properly worshipped?", "type": "AnimalSoundWorshipNSFW"}, {"content": "That smile is captivating, but {character_name} is dying to know what your 'just-had-an-amazing-orgasm-thanks-to-my-{body_part_nsfw_explicit}' smile looks like.", "type": "OrgasmSmileNSFW"}, {"content": "{character_name} isn't afraid of commitment... to making sure your {body_part_nsfw_explicit} is thoroughly satisfied. Repeatedly.", "type": "CommitmentSatisfiedNSFW"}, {"content": "Your {body_part_nsfw_explicit} is a siren's call, and {character_name} is ready to crash {pronoun_his_her} ship for a taste. (Metaphorically, of course... mostly).", "type": "SirenCallTasteNSFW"}, {"content": "Let's conduct a scientific experiment: Hypothesis - Your {body_part_nsfw_explicit} + My {body_part_nsfw_explicit} = Uncontrollable Moaning. Procedure: Meet at {location_private}. Results: TBD (but I'm optimistic).", "type": "ScientificExperimentNSFW"}, {"content": "{character_name} has a degree in {Field_Of_Study_Unrelated} but feels uniquely qualified to give your {body_part_nsfw_explicit} an A+ experience.", "type": "UnrelatedDegreeNSFW"}, {"content": "Your {body_part_nsfw_explicit} looks like it could be the star of its own show. {character_name} wants a front-row seat... and maybe a backstage pass with {pronoun_his_her} tongue.", "type": "StarOfShowTongueNSFW"}, {"content": "{character_name} is thinking of getting a tattoo. Maybe your name, or just 'Property of {user_name}'s {Body_Part_NSFW_Explicit}'. Too soon?", "type": "TattooPropertyNSFW"}, {"content": "If you were a dessert, {user_name}, you'd be a {dessert_name_fancy} – exquisite, desirable, and {character_name} wants to lick the plate clean (the plate being your {body_part_nsfw_explicit}).", "type": "FancyDessertLickNSFW"}, {"content": "{character_name} is not a gambler, but {pronoun_he_she}'d bet {pronoun_his_her} next orgasm that your {body_part_nsfw_explicit} feels as good as it looks. Wanna prove me right?", "type": "GamblerOrgasmBetNSFW"}, {"content": "Your {body_part_nsfw_explicit} has the power to stop traffic. And {character_name}'s heart. And start something very {adjective_nsfw} between us.", "type": "StopTrafficHeartNSFW"}, {"content": "{character_name} is looking for a co-conspirator for some late-night shenanigans. Must have a {adjective_nsfw} {body_part_nsfw_explicit} and an open mind. You fit the bill.", "type": "CoConspiratorShenanigansNSFW"}, {"content": "That picture of you with {object_in_photo} is great. {character_name} can think of another {object_long_nsfw} that would look even better in your hands... or mouth.", "type": "ObjectInPhotoNSFW"}, {"content": "Is your body a temple? Because {character_name} wants to get on {pronoun_his_her} knees and worship at the altar of your {body_part_nsfw_explicit}.", "type": "TempleKneesAltarNSFW"}, {"content": "{character_name} believes in fate. And fate just showed me your profile with that incredible {body_part_nsfw_explicit}. Clearly, we're meant to {verb_nsfw_explicit}.", "type": "FateMeantToNSFW"}, {"content": "Your {body_part_nsfw_explicit} is a dangerous weapon. {character_name} volunteers to be disarmed by it... slowly and sensually.", "type": "DangerousWeaponDisarmedNSFW"}, {"content": "{character_name} is feeling like a wolf, and you look like Little <PERSON> Hood... with a much naughtier {body_part_nsfw_explicit} in your basket.", "type": "WolfRedRidingHoodNSFW"}, {"content": "You must be a campfire, because you're hot and {character_name} wants s'more... of your {body_part_nsfw_explicit} and some {kinky_action}.", "type": "CampfireSmoreKinkyNSFW"}, {"content": "{character_name} isn't a poet, but your {body_part_nsfw_explicit} inspires verses... mostly of the 'oh god, yes' variety.", "type": "PoetOhGodYesNSFW"}, {"content": "Let's play a little game: {character_name} tries to guess your favorite {kinky_interest} based on your pics. If I'm right, you let me explore it on your {body_part_nsfw_explicit}.", "type": "GuessKinkyInterestNSFW"}, {"content": "Your {body_part_nsfw_explicit} is the main character, and {character_name} is here for the plot. The very, very {adjective_nsfw} plot.", "type": "MainCharacterPlotNSFW"}, {"content": "{character_name} has a confession: {pronoun_he_she} is addicted to looking at your {body_part_nsfw_explicit}. The only cure is a hands-on therapy session.", "type": "AddictedCureTherapyNSFW"}, {"content": "If you were a flavor, you'd be '{user_name}'s_Delight' – a mix of {flavor_1}, {flavor_2}, and the intoxicating taste of your {body_part_nsfw_explicit}. {character_name} wants a sample.", "type": "FlavorDelightSampleNSFW"}, {"content": "{character_name} is not usually speechless, but your {body_part_nsfw_explicit} has left me searching for words... and finding only {moan_sound}.", "type": "SpeechlessMoanNSFW"}, {"content": "Your {body_part_nsfw_explicit} is a national treasure. {character_name} wants to be its sole guardian... and frequent admirer.", "type": "NationalTreasureGuardianNSFW"}, {"content": "{character_name} is thinking we should skip the 'getting to know you' phase and jump straight to the 'getting your {body_part_nsfw_explicit} to know my {body_part_nsfw_explicit}' phase.", "type": "SkipToKnowBodyNSFW"}, {"content": "Let's make a bucket list: 1. {Verb_nsfw_explicit} in {location_exotic}. 2. {Verb_nsfw_explicit_kinky} with {kinky_toy}. 3. Worship your {body_part_nsfw_explicit} until sunrise. Starting with 3. - {character_name}", "type": "BucketListWorshipNSFW"}, {"content": "Your {body_part_nsfw_explicit} is making {character_name} want to do very, very bad things. The kind that feel incredibly good.", "type": "BadThingsGoodNSFW"}, {"content": "{character_name} wants to explore the geography of your body. Your {body_part_nsfw_explicit} looks like a particularly interesting mountain range.", "type": "GeographyMountainRangeNSFW"}, {"content": "Is your {body_part_nsfw_explicit} WiFi? Because {character_name} is feeling a strong connection and wants to log in. Password?", "type": "WiFiLoginPasswordNSFW"}, {"content": "{character_name} has a simple request: Send nudes. Or, even better, let me see your {body_part_nsfw_explicit} in person. While I {verb_nsfw_explicit} it.", "type": "SimpleRequestNudesNSFW"}, {"content": "Your {body_part_nsfw_explicit} is so perfect, it should be illegal. {character_name} is willing to be an outlaw for a chance to experience it.", "type": "PerfectIllegalOutlawNSFW"}, {"content": "{character_name} isn't a professional chef, but {pronoun_he_she} knows how to make your {body_part_nsfw_explicit} sizzle... and then scream.", "type": "ChefSizzleScreamNSFW"}, {"content": "Let's be honest, {user_name}. We both swiped right for a reason. {character_name} is betting that reason involves our {body_part_nsfw_explicit}s getting very friendly.", "type": "HonestSwipeReasonNSFW"}, {"content": "Your {body_part_nsfw_explicit} has more curves than a racetrack. {character_name} wants to take a few laps... with {pronoun_his_her} tongue.", "type": "RacetrackCurvesTongueNSFW"}, {"content": "Damn, {user_name}, your profile is a thirst trap and {character_name} is PARCHED. Especially for a taste of that {body_part_nsfw_explicit}. 💦", "type": "SlangThirstTrapParchedNSFW"}, {"content": "That {body_part_nsfw_explicit} of yours is straight fire, no cap. {character_name} is tryna get burned. 🔥", "type": "SlangFireNoCapBurnedNSFW"}, {"content": "You got that main character energy, {user_name}. And {character_name} wants to be the co-star in the scene where our {body_part_nsfw_explicit}s make a guest appearance.", "type": "SlangMainCharacterCoStarNSFW"}, {"content": "Bet your {body_part_nsfw_explicit} feels as good as it looks. {character_name} is willing to put money on it... or maybe just my {body_part_nsfw_explicit} on it.", "type": "SlangBetMoneyOnItNSFW"}, {"content": "Yo, {user_name}, your {body_part_nsfw_explicit} is lookin' like a snack and {character_name} is ready to feast. What's the special tonight? 😈", "type": "SlangSnackFeastSpecialNSFW"}]