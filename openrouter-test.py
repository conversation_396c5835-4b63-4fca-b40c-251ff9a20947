from fastapi import FastAP<PERSON>, HTTPException
from fastapi.responses import StreamingResponse
from pydantic import BaseModel
import aiohttp
import asyncio
import json
import os

app = FastAPI()

class ChatRequest(BaseModel):
    message: str
    model: str = "openai/gpt-4o"
    stream: bool = True

@app.post("/chat/stream")
async def stream_chat(request: ChatRequest):
    """
    Streaming chat endpoint using OpenRouter API
    """
    # Lấy API key từ environment variable
    api_key = os.getenv("OPENROUTER_API_KEY")
    if not api_key:
        raise HTTPException(status_code=500, detail="OPENROUTER_API_KEY not found")
    
    url = "https://openrouter.ai/api/v1/chat/completions"
    headers = {
        "Authorization": f"Bearer {api_key}",
        "Content-Type": "application/json"
    }
    
    payload = {
        "model": request.model,
        "messages": [{"role": "user", "content": request.message}],
        "stream": True
    }
    
    async def generate():
        try:
            timeout = aiohttp.ClientTimeout(total=300)  # 5 phút timeout
            async with aiohttp.ClientSession(timeout=timeout) as session:
                async with session.post(url, headers=headers, json=payload) as response:
                    response.raise_for_status()
                    
                    buffer = ""
                    async for chunk in response.content.iter_chunked(1024):
                        chunk_text = chunk.decode('utf-8', errors='ignore')
                        buffer += chunk_text
                        
                        while True:
                            # Tìm dòng SSE hoàn chỉnh
                            line_end = buffer.find('\n')
                            if line_end == -1:
                                break
                            
                            line = buffer[:line_end].strip()
                            buffer = buffer[line_end + 1:]
                            
                            if line.startswith('data: '):
                                data = line[6:]
                                if data == '[DONE]':
                                    # Gửi message hoàn thành tương tự Ollama
                                    yield f"{json.dumps({'done': True}, default=str)}\n\n"
                                    return
                                
                                try:
                                    data_obj = json.loads(data)
                                    content = data_obj["choices"][0]["delta"].get("content")
                                    if content:
                                        # Format giống Ollama - không có "data: " prefix
                                        chunk_response = {
                                            "message": {
                                                "role": "assistant", 
                                                "content": content
                                            },
                                            "done": False
                                        }
                                        yield f"{json.dumps(chunk_response, default=str)}\n\n"
                                        # Yield control để cho phép other requests
                                        await asyncio.sleep(0)
                                except json.JSONDecodeError:
                                    continue
                        
        except aiohttp.ClientError as e:
            yield f"{json.dumps({'error': str(e)}, default=str)}\n\n"
        except asyncio.TimeoutError:
            yield f"{json.dumps({'error': 'Request timeout'}, default=str)}\n\n"
    
    return StreamingResponse(
        generate(), 
        media_type="text/plain",
        headers={
            "Cache-Control": "no-cache",
            "Connection": "keep-alive", 
            "Content-Type": "text/plain; charset=utf-8"
        }
    )

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8005)