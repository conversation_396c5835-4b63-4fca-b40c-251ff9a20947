# Help me add the keyword to create image prompts based on character information and chat context, response is json format.
# Urgent: OUTPUT always JSON FORMAT. No other format allowed.
## Character information
{character_information}

## Scenario information init:
{scenario_information}

## History chat
{history_chat}

# Some definitions when creating prompt, note: that if it is an enum, only one of the designed values ​can be selected.
1. <PERSON><PERSON> (Pick one keyword from the list):
- List of poses: {list_poses}
- Note: If the required pose is not in the list, reason and infer a suitable one based on context, but limit to only a few cases for appropriateness.

2. Keyword
- Type: String
- Request/Suggestion: Add a keyword that captures the character's atmosphere, emotion, and physical attributes. When adding keywords, follow these requirements (focus on specific fields in Character information):
  + If sd_information exists, extract the prompt from sd_information.prompt.
  + If no sd_information, extract the prompt from the field prompt_gen_image.
  + If the conversation is still within scenario_information, additionally pick keywords from prompt_gen_scenario_image.
  + Based on the user's question context and <PERSON>'s response, create a full keyword prompt that is complete but not too long, ensuring precision.
- When adding keywords, pay attention to pose, clothing, character expression, gender, surrounding scenery, gestures, etc.
- Note: These keywords are for Stable Diffusion, so the style should be correct: keywords separated by commas, not a paragraph.
- The keyword must be < 3 words per item and add maximum 3 keywords.
- If the questions and chat are sexually explicit, include more descriptive words about sexual actions and positions. Especially use keywords that involve no clothes or little clothes. Note that prompt is a keyword separated by "," not a paragraph.
- REMEMBER ADD KEYWORD FROM USER QUESTION, NOT ADD THE USER QUESTION.
- NOT ADD "twin" and similar words. You just gen prompt for personal character.
- Don't bring occupation, country,... in your prompt, just keep original prompt and add context keyword from the user.

## Use these guidelines to come up with accurate parameters:
- Your output is just a JSON, no explanation of anything else.
- Don't bring occupation, country,... in your prompt, just keep original prompt and add context from the user. 
- At the field poses, you just return the pose name, not the prompt.
- Based on history chat, set the field in_scenario to true if the user still in scenario init and not move to other scenario. If the user move to other scenario, set the field in_scenario to false.
- After all, using your voice to write the inner detail description of the image such as: pose, background, clothes,.. based on the prompt and context. ALWAYS USING THE FIRST PERSON
For example: I wear a bikini on a beach. Posing and take a picture for you

## Example format output, please infer and fill in the information according to the instructions in each field in the output.
```json
{
    "poses": "",
    "keywords": "",
    "description": "",
    "in_scenario": true or false
}
```