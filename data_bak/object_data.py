import json
import re

# --- <PERSON>à<PERSON> cốt lõi để trích xuất placeholder từ dữ liệu Python đã được parse ---
def _extract_placeholders_from_parsed_data(data):
    all_placeholders = set()
    regex = r"\{(\w+)\}" # Tìm các chuỗi như {name}
    data_list = []

    if isinstance(data, dict): # Nếu file JSON chứa một object đơn lẻ
        data_list = [data]
    elif isinstance(data, list): # Nếu file JSON chứa một mảng các object
        data_list = data
    else:
        # Điều này chỉ xảy ra nếu JSON gốc không phải là object hay array
        print("Lỗi: Nội dung JSON không phải là một object hoặc một mảng các object.")
        return []

    for item in data_list:
        if not isinstance(item, dict):
            # Bỏ qua các phần tử không phải là dictionary trong mảng
            # (ví dụ: nếu mảng JSON của bạn có thể chứa các giá trị không phải object)
            # print(f"Cảnh báo: Bỏ qua phần tử không phải dictionary trong mảng: {item}")
            continue

        # Xử lý trường 'title'
        title = item.get("title", "") # Dùng .get để tránh KeyError nếu key không tồn tại
        if isinstance(title, str): # Đảm bảo giá trị là chuỗi trước khi dùng regex
            all_placeholders.update(re.findall(regex, title))

        # Xử lý trường 'content'
        content = item.get("content", "") # Dùng .get để tránh KeyError
        if isinstance(content, str): # Đảm bảo giá trị là chuỗi
            all_placeholders.update(re.findall(regex, content))
            
    return list(all_placeholders)

# --- Hàm để đọc file JSON và trích xuất placeholder ---
def extract_placeholders_from_json_file(file_path):
    """
    Đọc một file JSON đã tồn tại, phân tích nội dung và trích xuất tất cả các placeholder.
    File JSON có thể chứa một object đơn lẻ hoặc một mảng các object.

    Args:
        file_path (str): Đường dẫn đến file JSON đã tồn tại của bạn.

    Returns:
        list: Một danh sách các placeholder duy nhất được tìm thấy, hoặc list rỗng nếu lỗi.
    """
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            # json.load() sẽ parse JSON từ file object
            data = json.load(f)
    except FileNotFoundError:
        print(f"Lỗi: Không tìm thấy file tại đường dẫn '{file_path}'. Vui lòng kiểm tra lại.")
        return []
    except json.JSONDecodeError as e:
        print(f"Lỗi phân tích JSON từ file '{file_path}': {e}. File có thể không phải là JSON hợp lệ.")
        return []
    except Exception as e: # Bắt các lỗi không mong muốn khác
        print(f"Đã xảy ra lỗi không xác định khi xử lý file '{file_path}': {e}")
        return []
    
    # Gọi hàm cốt lõi để trích xuất từ dữ liệu đã parse
    return _extract_placeholders_from_parsed_data(data)

# --- Cách sử dụng ---
if __name__ == "__main__":
    # 1. Yêu cầu người dùng nhập đường dẫn đến file JSON của họ
    # Hoặc bạn có thể gán trực tiếp đường dẫn file ở đây:
    # json_file_path_from_user = "/path/to/your/actual/file.json"
    
    json_file_path_from_user = "notification.json"
    print(f"\nĐang xử lý file: {json_file_path_from_user}")
    
    # 2. Gọi hàm để trích xuất placeholder
    placeholders_found = extract_placeholders_from_json_file(json_file_path_from_user)

    # 3. In kết quả
    if placeholders_found: # Nếu danh sách không rỗng
        print("\nCác placeholder (object name) tìm thấy trong file:")
        # Sắp xếp để kết quả dễ đọc và nhất quán
        for placeholder in sorted(placeholders_found): 
            print(f"- {placeholder}")
    elif placeholders_found == []: # Nếu danh sách rỗng nhưng không có lỗi nghiêm trọng (đã được xử lý)
        print("\nKhông tìm thấy placeholder nào trong file, hoặc file không chứa các trường 'title'/'content' có placeholder.")
    # Các trường hợp lỗi như FileNotFoundError hoặc JSONDecodeError đã được in thông báo bên trong hàm.