# Mini Chat Foxy

Ứng dụng chat đa mô hình với tích hợp API endpoint.

## Cài đặt

1. Clone repository và di chuyển vào thư mục:
```bash
cd mini-chat-foxy
```

2. Cài đặt dependencies:
```bash
pip install -r requirements.txt
```

3. Cấu hình biến môi trường:
```bash
cp .env.example .env
```

Sau đó chỉnh sửa file `.env` với các thông tin của bạn:

```env
# API Configuration
API_ENDPOINT=https://your-api-endpoint.com/chat
API_TIMEOUT=30

# User Configuration  
USER_ID=your_user_id_here

# Application Configuration
SECRET_KEY=your-secret-key-change-in-production
DEBUG=True
HOST=0.0.0.0
PORT=5000

# Session Configuration
SESSION_TIMEOUT_HOURS=24
CLEANUP_INTERVAL_HOURS=1
```

## Chạy ứng dụng

```bash
python app.py
```

Ứng dụng sẽ chạy tại `http://localhost:5000`

## API Endpoint Format

Ứng dụng sẽ gửi dữ liệu đến API endpoint với định dạng sau:

```json
{
  "query": "string",
  "user_id": "string", 
  "character_id": "string",
  "session_id": "string",
  "prompt_id": "string",
  "scenario_mode": true,
  "model_name": "string"
}
```

### Mô tả các trường:

- `query`: Tin nhắn từ người dùng
- `user_id`: ID người dùng (được cấu hình trong `.env`)
- `character_id`: ID của character được chọn trên UI
- `session_id`: ID session của trình duyệt
- `prompt_id`: ID của prompt version được chọn
- `scenario_mode`: Luôn là `true`
- `model_name`: Tên model được chọn

## API Routes

### Chat
- `POST /api/chat` - Gửi tin nhắn chat

### Models & Characters
- `GET /api/models` - Lấy danh sách models
- `GET /api/characters` - Lấy danh sách characters  
- `GET /api/prompt-versions` - Lấy danh sách prompt versions

### Session Management
- `POST /api/session/new` - Tạo session mới
- `GET /api/session/info` - Thông tin session hiện tại
- `GET /api/session/history` - Lịch sử chat của session
- `POST /api/session/cleanup` - Dọn dẹp sessions hết hạn

### Testing
- `POST /api/test-endpoint` - Test API endpoint

## Test API Endpoint

Bạn có thể test API endpoint bằng cách gửi POST request đến `/api/test-endpoint`:

```bash
curl -X POST http://localhost:5000/api/test-endpoint \
  -H "Content-Type: application/json" \
  -d '{
    "query": "Hello test",
    "model": "gpt-4",
    "character": "assistant",
    "prompt_version": "v1"
  }'
```

## Cấu trúc dự án

```
mini-chat-foxy/
├── app.py              # Main Flask application
├── requirements.txt    # Python dependencies
├── .env               # Environment variables (tạo từ .env.example)
├── .env.example       # Template cho environment variables
├── README.md          # Documentation
├── static/            # Static files (CSS, JS)
│   ├── css/
│   └── js/
└── templates/         # HTML templates
    └── index.html
```

## Lưu ý

- Đảm bảo API endpoint của bạn có thể nhận POST request với JSON payload theo định dạng trên
- Ứng dụng sẽ fallback về mock response nếu API endpoint không khả dụng
- Session được lưu trong memory, sẽ mất khi restart ứng dụng
- Trong production, nên sử dụng Redis hoặc database để lưu session
