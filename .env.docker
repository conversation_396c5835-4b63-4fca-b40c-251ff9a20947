# Docker Environment Configuration for Mini Chat Foxy
# Copy this file to .env and modify the values as needed

# API Configuration
API_ENDPOINT=http://localhost:8765/completion-test
API_TIMEOUT=300

# User Configuration
USER_ID=default_user_123

# Application Configuration
SECRET_KEY=your-secret-key-change-in-production-please
DEBUG=True
HOST=0.0.0.0
PORT=8080

# Session Configuration
SESSION_TIMEOUT_HOURS=24
CLEANUP_INTERVAL_HOURS=1
