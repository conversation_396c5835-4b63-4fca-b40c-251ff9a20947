import json
import collections
import matplotlib.pyplot as plt # Still useful for potentially other plots, or if user wants bar chart too
from wordcloud import Word<PERSON>loud # Import the WordCloud library
import os

def analyze_json_categories(file_path):
    """
    Analyzes a JSON file to count the occurrences of different 'type' categories.

    Args:
        file_path (str): The path to the JSON file.

    Returns:
        collections.Counter: A Counter object with category counts,
                             or None if an error occurs.
    """
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            data = json.load(f)
    except FileNotFoundError:
        print(f"Error: The file '{file_path}' was not found.")
        return None
    except json.JSONDecodeError:
        print(f"Error: The file '{file_path}' is not a valid JSON file.")
        return None
    except Exception as e:
        print(f"An unexpected error occurred while reading the file: {e}")
        return None

    if not isinstance(data, list):
        print(f"Error: The JSON data in '{file_path}' is not an array (list) of objects.")
        return None

    if not data:
        print(f"The JSON file '{file_path}' is empty or contains an empty list.")
        return collections.Counter()

    category_list = []
    for item in data:
        if isinstance(item, dict):
            category = item.get("type", "UNKNOWN_TYPE")
            category_list.append(category)
        else:
            print(f"Warning: Skipping non-object item in JSON array: {item}")
            category_list.append("INVALID_ITEM_FORMAT")

    return collections.Counter(category_list)

def plot_category_distribution_barchart(category_counts, chart_filename="category_barchart.png"):
    """
    Generates and saves a bar chart of category distributions.
    (Keeping this function in case it's still useful)
    """
    if not category_counts:
        print("No category data to plot for bar chart.")
        return

    categories = list(category_counts.keys())
    counts = list(category_counts.values())

    plt.figure(figsize=(12, 7))
    bars = plt.bar(categories, counts, color='skyblue')
    for bar in bars:
        yval = bar.get_height()
        plt.text(bar.get_x() + bar.get_width()/2.0, yval + 0.05 * max(counts),
                 f'{int(yval)}', ha='center', va='bottom')

    plt.xlabel("Category Type")
    plt.ylabel("Number of Occurrences")
    plt.title("Distribution of Categories (Bar Chart)")
    plt.xticks(rotation=45, ha="right")
    plt.tight_layout()

    try:
        plt.savefig(chart_filename)
        print(f"Bar chart saved as '{chart_filename}'")
    except Exception as e:
        print(f"Error saving bar chart: {e}")
    plt.close() # Close the plot to free up memory

def generate_category_wordcloud(category_counts, cloud_filename="category_wordcloud.png"):
    """
    Generates and saves a word cloud from category counts.

    Args:
        category_counts (collections.Counter or dict): Dictionary/Counter with categories as keys and counts as values.
        cloud_filename (str): Filename to save the word cloud image.
    """
    if not category_counts:
        print("No category data to generate word cloud.")
        return

    # The WordCloud object takes a dictionary of word frequencies
    # Counter is a subclass of dict, so it works directly
    wc = WordCloud(width=800, height=400, background_color='black', colormap='viridis')
    wc.generate_from_frequencies(category_counts)

    plt.figure(figsize=(10, 5))
    plt.imshow(wc, interpolation='bilinear')
    plt.axis('off') # Don't show axes for a word cloud
    plt.tight_layout(pad=0)

    try:
        plt.savefig(cloud_filename)
        print(f"Word cloud saved as '{cloud_filename}'")
    except Exception as e:
        print(f"Error saving word cloud: {e}")
    plt.close() # Close the plot

def main():
    """
    Main function to run the analysis.
    """
    # --- Configuration ---
    json_file_path = 'notification.json' # Replace with your JSON file path
    output_wordcloud_filename = 'category_wordcloud.png'
    output_barchart_filename = 'category_distribution_barchart.png' # Optional bar chart
    # --- End Configuration ---

    if not os.path.exists(json_file_path):
        print(f"'{json_file_path}' not found. Creating a dummy file for demonstration.")

    category_counts = analyze_json_categories(json_file_path)

    if category_counts is not None:
        if not category_counts:
            print("No categories found or the file was empty.")
        else:
            num_unique_categories = len(category_counts)
            print(f"\n--- Category Analysis ---")
            print(f"Number of unique categories: {num_unique_categories}")
            print("\nDistribution of categories:")
            for category, count in category_counts.items():
                print(f"- {category}: {count}")

            print(f"\nAttempting to generate word cloud...")
            generate_category_wordcloud(category_counts, output_wordcloud_filename)

            # Optional: You can also generate the bar chart if you want both
            # print(f"\nAttempting to generate bar chart...")
            # plot_category_distribution_barchart(category_counts, output_barchart_filename)

if __name__ == '__main__':
    main()