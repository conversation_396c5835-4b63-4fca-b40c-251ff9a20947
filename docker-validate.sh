#!/bin/bash

# Simple Docker Setup Validation Script for Mini Chat Foxy

echo "🐳 Mini Chat Foxy - Docker Setup Validation"
echo "==========================================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Function to print status
print_status() {
    if [ $1 -eq 0 ]; then
        echo -e "${GREEN}✅ $2${NC}"
    else
        echo -e "${RED}❌ $2${NC}"
    fi
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

# Check Docker installation
echo ""
echo "1. Checking Docker installation..."
if command -v docker &> /dev/null; then
    DOCKER_VERSION=$(docker --version 2>/dev/null)
    print_status 0 "Docker is installed: $DOCKER_VERSION"
else
    print_status 1 "Docker is not installed"
fi

# Check Docker Compose
echo ""
echo "2. Checking Docker Compose..."
if command -v docker-compose &> /dev/null || docker compose version &> /dev/null; then
    print_status 0 "Docker Compose is available"
else
    print_status 1 "Docker Compose is not available"
fi

# Check Docker daemon
echo ""
echo "3. Checking Docker daemon..."
if docker info &> /dev/null; then
    print_status 0 "Docker daemon is running"
else
    print_status 1 "Docker daemon is not running"
fi

# Check required files
echo ""
echo "4. Checking required files..."
files=("Dockerfile" "docker-compose.yml" ".dockerignore" "requirements.txt" ".env")

for file in "${files[@]}"; do
    if [ -f "$file" ]; then
        print_status 0 "$file exists"
    else
        if [ "$file" == ".env" ]; then
            print_warning "$file not found - copy from .env.docker"
        else
            print_status 1 "$file not found"
        fi
    fi
done

# Check environment configuration
echo ""
echo "5. Checking environment configuration..."
if [ -f ".env" ]; then
    print_status 0 ".env file exists"
    
    required_vars=("API_ENDPOINT" "USER_ID" "SECRET_KEY")
    for var in "${required_vars[@]}"; do
        if grep -q "^$var=" .env; then
            print_status 0 "$var is configured"
        else
            print_warning "$var not found in .env"
        fi
    done
else
    print_warning ".env file not found"
fi

# Check port availability
echo ""
echo "6. Checking port availability..."
if lsof -Pi :8080 -sTCP:LISTEN -t >/dev/null 2>&1; then
    print_warning "Port 8080 is already in use"
else
    print_status 0 "Port 8080 is available"
fi

# Validate Docker Compose file
echo ""
echo "7. Validating Docker Compose configuration..."
if command -v docker-compose &> /dev/null; then
    if docker-compose config &> /dev/null; then
        print_status 0 "docker-compose.yml is valid"
    else
        print_status 1 "docker-compose.yml has errors"
    fi
fi

# Summary
echo ""
echo "==========================================="
echo "🎯 Setup Summary"
echo "==========================================="

if [ -f ".env" ]; then
    echo "📝 Configuration:"
    echo "   API Endpoint: $(grep '^API_ENDPOINT=' .env 2>/dev/null | cut -d'=' -f2 || echo 'Not configured')"
    echo "   User ID: $(grep '^USER_ID=' .env 2>/dev/null | cut -d'=' -f2 || echo 'Not configured')"
    echo "   Port: $(grep '^PORT=' .env 2>/dev/null | cut -d'=' -f2 || echo '8080')"
fi

echo ""
echo "🚀 Quick Start Command:"
echo "   docker-compose up -d --build"

echo ""
echo "📚 Documentation: DOCKER_README.md"

echo ""
if docker info &> /dev/null; then
    echo -e "${GREEN}✅ Ready to run Docker containers!${NC}"
else
    echo -e "${YELLOW}⚠️  Start Docker daemon to proceed${NC}"
fi

echo "==========================================="
