// Global variables
let messageCount = 0;
let isLoading = false;
let sessionId = window.SESSION_ID || null;
let chatHistory = window.CHAT_HISTORY || [];

// LocalStorage keys
const STORAGE_KEYS = {
    CHAT_HISTORY: 'mini_chat_foxy_history',
    SESSION_ID: 'mini_chat_foxy_session_id',
    MESSAGE_COUNT: 'mini_chat_foxy_message_count'
};

// Character data
const characters = {
    '001_hinata': {
        name: 'Hi<PERSON><PERSON>',
        personality: '<PERSON><PERSON><PERSON> is Submissive'
    },
    '001_taesung': {
        name: '<PERSON>e<PERSON><PERSON>',
        personality: 'Tae<PERSON>ung is Dominant'
    }
};

// Prompt version data
const promptVersions = {
    'v1': {
        name: 'Version 1.0',
        description: 'Standard prompt'
    },
    'v2': {
        name: 'Version 2.0',
        description: 'Enhanced with context'
    },
    'v3': {
        name: 'Version 3.0',
        description: 'Creative and detailed'
    },
    'v4': {
        name: 'Version 4.0',
        description: 'Technical and precise'
    },
    'v5': {
        name: 'Version 5.0',
        description: 'Conversational style'
    }
};

// Verbosity levels
const verbosityLevels = {
    1: 'Minimal',
    2: 'Normal',
    3: 'Verbose',
    4: 'Very Verbose'
};

// Number of sentences mapping for verbosity levels
const verbositySentences = {
    1: 5,   // Minimal
    2: 8,   // Normal
    3: 12,  // Verbose
    4: 15   // Very Verbose
};

// DOM elements
const messageInput = document.getElementById('message-input');
const sendChatButton = document.getElementById('send-chat-button');
const sendContinueButton = document.getElementById('send-continue-button');
const verbositySlider = document.getElementById('verbosity-slider');
const verbosityLabel = document.getElementById('verbosity-label');
const messagesContainer = document.getElementById('messages-container');
const loadingContainer = document.getElementById('loading-container');
const characterSelect = document.getElementById('character-select');
const currentCharacterSpan = document.getElementById('current-character');
const characterDescription = document.getElementById('character-description');
const promptVersionSelect = document.getElementById('prompt-version-select');
const versionDescription = document.getElementById('version-description');
const selectedCountSpan = document.getElementById('selected-count');
const messageCountSpan = document.getElementById('message-count');
const loadingModelsSpan = document.getElementById('loading-models');

// LocalStorage functions
function saveToLocalStorage(key, data) {
    try {
        localStorage.setItem(key, JSON.stringify(data));
    } catch (error) {
        console.error('Error saving to localStorage:', error);
    }
}

function loadFromLocalStorage(key, defaultValue = null) {
    try {
        const data = localStorage.getItem(key);
        return data ? JSON.parse(data) : defaultValue;
    } catch (error) {
        console.error('Error loading from localStorage:', error);
        return defaultValue;
    }
}

function clearLocalStorage() {
    try {
        Object.values(STORAGE_KEYS).forEach(key => {
            localStorage.removeItem(key);
        });
    } catch (error) {
        console.error('Error clearing localStorage:', error);
    }
}

// Initialize app
document.addEventListener('DOMContentLoaded', function() {
    setupEventListeners();
    updateSelectedModelsCount();
    updateCharacterInfo();
    updateVersionInfo();
    updateVerbosityLabel();
    loadChatHistory();
});

function loadStoredData() {
    // Only load from localStorage if we have no server-side history
    if (chatHistory.length === 0) {
        const storedSessionId = loadFromLocalStorage(STORAGE_KEYS.SESSION_ID);
        const storedChatHistory = loadFromLocalStorage(STORAGE_KEYS.CHAT_HISTORY, []);

        // Only use localStorage data if it's for the same session
        if (storedSessionId === sessionId && storedChatHistory.length > 0) {
            console.log('Loading chat history from localStorage');
            chatHistory = [...storedChatHistory]; // Create a copy to avoid reference issues
            messageCount = storedChatHistory.length;
        }
    } else {
        console.log('Using server-side chat history');
        // If we have server-side history, sync it to localStorage
        saveToLocalStorage(STORAGE_KEYS.CHAT_HISTORY, chatHistory);
        saveToLocalStorage(STORAGE_KEYS.SESSION_ID, sessionId);
        saveToLocalStorage(STORAGE_KEYS.MESSAGE_COUNT, chatHistory.length);
    }
}

function setupEventListeners() {
    // Send button clicks
    sendChatButton.addEventListener('click', () => sendMessage('chat'));
    sendContinueButton.addEventListener('click', () => sendMessage('continue'));

    // Enter key press (updated for textarea) - defaults to normal chat
    messageInput.addEventListener('keydown', function(e) {
        if (e.key === 'Enter' && !e.shiftKey) {
            e.preventDefault();
            sendMessage('chat');
        }
    });

    // Verbosity slider change
    verbositySlider.addEventListener('input', updateVerbosityLabel);

    // Character selection change
    characterSelect.addEventListener('change', updateCharacterInfo);

    // Prompt version selection change
    promptVersionSelect.addEventListener('change', updateVersionInfo);

    // Model checkbox changes
    document.querySelectorAll('.model-checkbox').forEach(checkbox => {
        checkbox.addEventListener('change', updateSelectedModelsCount);
    });
}

function updateCharacterInfo() {
    const selectedCharacter = characterSelect.value;
    const character = characters[selectedCharacter];

    if (character) {
        currentCharacterSpan.textContent = character.name;
        characterDescription.textContent = character.personality;
    }
}

function updateVersionInfo() {
    const selectedVersion = promptVersionSelect.value;
    const version = promptVersions[selectedVersion];

    if (version) {
        versionDescription.textContent = version.description;
    }
}

function updateVerbosityLabel() {
    const verbosityValue = parseInt(verbositySlider.value);
    const verbosityText = verbosityLevels[verbosityValue] || 'Normal';
    verbosityLabel.textContent = verbosityText;
}

function updateSelectedModelsCount() {
    const selectedModels = document.querySelectorAll('.model-checkbox:checked');
    selectedCountSpan.textContent = selectedModels.length;
    loadingModelsSpan.textContent = selectedModels.length;
}

function loadChatHistory() {
    // First try to load from localStorage if no server-side history
    loadStoredData();

    if (chatHistory && chatHistory.length > 0) {
        // Hide welcome message
        const welcomeMessage = document.querySelector('.welcome-message');
        if (welcomeMessage) {
            welcomeMessage.style.display = 'none';
        }

        // Clear existing messages to prevent duplication
        const messagesContainer = document.getElementById('messages-container');
        const existingMessages = messagesContainer.querySelectorAll('.chat-entry');
        existingMessages.forEach(msg => msg.remove());

        // Load each chat entry
        chatHistory.forEach(chatEntry => {
            displayChatEntry(chatEntry);
        });

        // Update message count
        messageCount = chatHistory.length;
        messageCountSpan.textContent = messageCount;

        scrollToBottom();
    }
}

function displayChatEntry(chatEntry) {
    // Add user message
    const messageElement = document.createElement('div');
    messageElement.className = 'message-group chat-entry';

    // Format timestamp properly
    let formattedTime;
    try {
        const timestamp = chatEntry.timestamp || new Date().toISOString();
        formattedTime = new Date(timestamp).toLocaleString('en-US', {
            year: 'numeric',
            month: '2-digit',
            day: '2-digit',
            hour: '2-digit',
            minute: '2-digit',
            second: '2-digit'
        });
    } catch (error) {
        formattedTime = new Date().toLocaleString();
    }

    // Get chat type and verbosity info
    const chatType = chatEntry.chat_type || 'chat';
    const verbosityLevel = chatEntry.verbosity_level || 2;
    const chatTypeIcon = chatType === 'continue' ? 'fas fa-brain' : 'fas fa-comment';
    const chatTypeText = chatType === 'continue' ? 'Continue' : 'Chat';
    const verbosityText = verbosityLevels[verbosityLevel] || 'Normal';

    messageElement.innerHTML = `
        <div class="user-message">
            <i class="fas fa-user me-2"></i>
            ${escapeHtml(chatEntry.user_message)}
            <small class="d-block mt-1 opacity-75">
                <i class="fas fa-clock me-1"></i>
                ${formattedTime}
                <br>
                <i class="${chatTypeIcon} me-1"></i>
                ${chatTypeText} • ${verbosityText}
            </small>
        </div>
    `;
    messagesContainer.appendChild(messageElement);

    // Add responses
    if (chatEntry.responses && chatEntry.responses.length > 0) {
        displayResponses(chatEntry.responses, false); // false = don't animate
    }
}

// Toggle response collapse/expand
function toggleResponse(cardId) {
    const card = document.getElementById(cardId);
    const toggleBtn = card.querySelector('.toggle-btn i');

    if (card.classList.contains('collapsed')) {
        // Expand
        card.classList.remove('collapsed');
        toggleBtn.className = 'fas fa-chevron-up';
        toggleBtn.parentElement.title = 'Collapse';
    } else {
        // Collapse
        card.classList.add('collapsed');
        toggleBtn.className = 'fas fa-chevron-down';
        toggleBtn.parentElement.title = 'Expand';
    }
}

// Add global function for collapse/expand all
function toggleAllResponses(collapse = null) {
    const responseCards = document.querySelectorAll('.response-card');

    responseCards.forEach(card => {
        const toggleBtn = card.querySelector('.toggle-btn i');
        const shouldCollapse = collapse !== null ? collapse : !card.classList.contains('collapsed');

        if (shouldCollapse) {
            card.classList.add('collapsed');
            toggleBtn.className = 'fas fa-chevron-down';
            toggleBtn.parentElement.title = 'Expand';
        } else {
            card.classList.remove('collapsed');
            toggleBtn.className = 'fas fa-chevron-up';
            toggleBtn.parentElement.title = 'Collapse';
        }
    });
}

// Make functions available globally
window.toggleResponse = toggleResponse;
window.toggleAllResponses = toggleAllResponses;

function getSelectedModels() {
    const selectedModels = [];
    document.querySelectorAll('.model-checkbox:checked').forEach(checkbox => {
        selectedModels.push(checkbox.value);
    });
    return selectedModels;
}

function sendMessage(chatType = 'chat') {
    if (isLoading) return;

    const message = messageInput.value.trim();
    if (!message) return;

    const selectedModels = getSelectedModels();
    if (selectedModels.length === 0) {
        alert('Please select at least one model!');
        return;
    }

    // Get verbosity level
    const verbosityLevel = parseInt(verbositySlider.value);

    // Clear input
    messageInput.value = '';

    // Hide welcome message
    const welcomeMessage = document.querySelector('.welcome-message');
    if (welcomeMessage) {
        welcomeMessage.style.display = 'none';
    }

    // Add user message with chat type indicator
    addUserMessage(message, chatType, verbosityLevel);

    // Show loading
    showLoading();

    // Send request
    sendChatRequest(message, selectedModels, characterSelect.value, promptVersionSelect.value, chatType, verbosityLevel);
}

function addUserMessage(message, chatType = 'chat', verbosityLevel = 2) {
    const messageElement = document.createElement('div');
    messageElement.className = 'message-group';

    const chatTypeIcon = chatType === 'continue' ? 'fas fa-brain' : 'fas fa-comment';
    const chatTypeText = chatType === 'continue' ? 'Continue' : 'Chat';
    const verbosityText = verbosityLevels[verbosityLevel] || 'Normal';

    messageElement.innerHTML = `
        <div class="user-message">
            <i class="fas fa-user me-2"></i>
            ${escapeHtml(message)}
            <small class="d-block mt-1 opacity-75">
                <i class="${chatTypeIcon} me-1"></i>
                ${chatTypeText} • ${verbosityText}
            </small>
        </div>
    `;

    messagesContainer.appendChild(messageElement);
    scrollToBottom();
}

function showLoading() {
    isLoading = true;
    loadingContainer.style.display = 'block';
    sendChatButton.disabled = true;
    sendContinueButton.disabled = true;
    scrollToBottom();
}

function hideLoading() {
    isLoading = false;
    loadingContainer.style.display = 'none';
    sendChatButton.disabled = false;
    sendContinueButton.disabled = false;
}

function sendChatRequest(message, models, character, promptVersion, chatType = 'chat', verbosityLevel = 2) {
    // Determine API endpoint based on chat type
    const apiEndpoint = chatType === 'continue' ? '/api/chat/continue' : '/api/chat';

    // Get number of sentences based on verbosity level
    const numberOfSentences = verbositySentences[verbosityLevel] || 8;

    fetch(apiEndpoint, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            message: message,
            models: models,
            character: character,
            prompt_version: promptVersion,
            session_id: sessionId,
            verbosity_level: verbosityLevel,
            chat_type: chatType,
            number_of_sentence: numberOfSentences
        })
    })
    .then(response => response.json())
    .then(data => {
        hideLoading();
        
        if (data.success) {
            displayResponses(data.responses, true); // true = animate
            messageCount++;
            messageCountSpan.textContent = messageCount;

            // Update session ID if provided
            if (data.session_id) {
                sessionId = data.session_id;
            }

            // Save to localStorage
            saveChatToLocalStorage(message, data.responses);
        } else {
            showError(data.error || 'An error occurred while sending the message!');
        }
    })
    .catch(error => {
        hideLoading();
        console.error('Error:', error);
        showError('Unable to connect to server!');
    });
}

function displayResponses(responses, animate = true) {
    const responsesElement = document.createElement('div');
    responsesElement.className = 'message-group';

    let responsesHtml = '<div class="responses-grid">';

    responses.forEach((response, index) => {
        const animationDelay = animate ? `style="animation-delay: ${index * 0.1}s"` : '';
        const cardId = `response-card-${Date.now()}-${index}`;
        const copyBtnId = `copy-btn-${Date.now()}-${index}`;

        // Store raw response data for copying
        const rawText = response.raw_response || response.response || '';

        responsesHtml += `
            <div class="response-card" id="${cardId}" ${animationDelay} data-raw-text="${escapeHtml(rawText)}">
                <div class="response-header" onclick="toggleResponse('${cardId}')">
                    <div class="header-left">
                        <span class="model-badge">
                            <i class="fas fa-robot me-1"></i>
                            ${response.model}
                        </span>
                        <button class="copy-btn" id="${copyBtnId}" onclick="copyResponse('${cardId}', '${copyBtnId}'); event.stopPropagation();" title="Copy raw text">
                            <i class="fas fa-copy"></i>
                            Copy
                        </button>
                    </div>
                    <button class="toggle-btn" title="Collapse/Expand">
                        <i class="fas fa-chevron-up"></i>
                    </button>
                </div>
                <div class="response-content">
                    ${response.response}
                </div>
            </div>
        `;
    });

    responsesHtml += '</div>';
    responsesElement.innerHTML = responsesHtml;

    messagesContainer.appendChild(responsesElement);
    scrollToBottom();
}

function showError(message) {
    const errorElement = document.createElement('div');
    errorElement.className = 'alert alert-danger mx-3';
    errorElement.innerHTML = `
        <i class="fas fa-exclamation-triangle me-2"></i>
        ${message}
    `;
    
    messagesContainer.appendChild(errorElement);
    scrollToBottom();
    
    // Auto remove error after 5 seconds
    setTimeout(() => {
        errorElement.remove();
    }, 5000);
}

function scrollToBottom() {
    setTimeout(() => {
        messagesContainer.scrollTop = messagesContainer.scrollHeight;
    }, 100);
}

function escapeHtml(text) {
    const div = document.createElement('div');
    div.textContent = text;
    return div.innerHTML;
}

// Add some fun interactions
document.addEventListener('keydown', function(e) {
    // Ctrl + Enter to send continue message
    if (e.ctrlKey && e.key === 'Enter') {
        sendMessage('continue');
    }

    // Escape to clear input
    if (e.key === 'Escape') {
        messageInput.value = '';
        messageInput.focus();
    }

    // Ctrl + Shift + C to collapse all responses
    if (e.ctrlKey && e.shiftKey && e.key === 'C') {
        e.preventDefault();
        toggleAllResponses(true);
    }

    // Ctrl + Shift + E to expand all responses
    if (e.ctrlKey && e.shiftKey && e.key === 'E') {
        e.preventDefault();
        toggleAllResponses(false);
    }
});

// Session Management Functions
function createNewSession() {
    fetch('/api/session/new', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            sessionId = data.session_id;
            // Reload page to start fresh session
            window.location.reload();
        }
    })
    .catch(error => {
        console.error('Error creating new session:', error);
    });
}

function getSessionInfo() {
    if (!sessionId) return;

    fetch(`/api/session/info?session_id=${sessionId}`)
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            console.log('Session Info:', data.session);
        }
    })
    .catch(error => {
        console.error('Error getting session info:', error);
    });
}

// Add session management to global scope for debugging
window.sessionManager = {
    createNew: createNewSession,
    getInfo: getSessionInfo,
    getCurrentSessionId: () => sessionId,
    getChatHistory: () => chatHistory
};

// LocalStorage chat management
function saveChatToLocalStorage(message, responses) {
    try {
        const chatEntry = {
            timestamp: new Date().toISOString(),
            user_message: message,
            responses: responses
        };

        // Add to in-memory chatHistory
        chatHistory.push(chatEntry);

        // Save entire chatHistory to localStorage (avoid duplication)
        saveToLocalStorage(STORAGE_KEYS.CHAT_HISTORY, chatHistory);
        saveToLocalStorage(STORAGE_KEYS.SESSION_ID, sessionId);
        saveToLocalStorage(STORAGE_KEYS.MESSAGE_COUNT, messageCount);
    } catch (error) {
        console.error('Error saving chat to localStorage:', error);
    }
}

// Reset Session functionality
function resetSession() {
    if (confirm('Are you sure you want to reset the session? This will clear all chat history and create a new session.')) {
        // Clear localStorage
        clearLocalStorage();

        // Create new session
        fetch('/api/session/new', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // Update session ID
                sessionId = data.session_id;

                // Clear chat display
                const messagesContainer = document.getElementById('messages-container');
                messagesContainer.innerHTML = `
                    <div class="welcome-message text-center text-muted">
                        <i class="fas fa-robot fa-3x mb-3"></i>
                        <h4>Welcome to Mini Chat Foxy!</h4>
                        <p>Select models and character, then send a message to start chatting.</p>
                    </div>
                `;

                // Reset counters and chat history
                messageCount = 0;
                chatHistory = []; // Clear in-memory chat history
                messageCountSpan.textContent = messageCount;

                // Update session ID display
                document.getElementById('session-id').textContent = sessionId.substring(0, 8) + '...';

                // Show success message
                showSuccessMessage('Session reset successfully! New session created.');

                // Save new session to localStorage
                saveToLocalStorage(STORAGE_KEYS.SESSION_ID, sessionId);
                saveToLocalStorage(STORAGE_KEYS.MESSAGE_COUNT, 0);
                saveToLocalStorage(STORAGE_KEYS.CHAT_HISTORY, []);
            } else {
                showError('Failed to create new session. Please try again.');
            }
        })
        .catch(error => {
            console.error('Error creating new session:', error);
            showError('Unable to connect to server for session reset!');
        });
    }
}

// Success message function
function showSuccessMessage(message) {
    const successElement = document.createElement('div');
    successElement.className = 'alert alert-success mx-3';
    successElement.innerHTML = `
        <i class="fas fa-check-circle me-2"></i>
        ${message}
    `;

    messagesContainer.appendChild(successElement);
    scrollToBottom();

    // Auto remove success message after 3 seconds
    setTimeout(() => {
        successElement.remove();
    }, 3000);
}

// Copy response functionality
function copyResponse(cardId, btnId) {
    const card = document.getElementById(cardId);
    const btn = document.getElementById(btnId);

    if (!card || !btn) {
        console.error('Card or button not found');
        return;
    }

    // Get raw text from data attribute
    const rawText = card.getAttribute('data-raw-text');

    if (!rawText) {
        console.error('No raw text found');
        return;
    }

    // Decode HTML entities
    const decodedText = decodeHtmlEntities(rawText);

    // Copy to clipboard
    if (navigator.clipboard && window.isSecureContext) {
        // Modern async clipboard API
        navigator.clipboard.writeText(decodedText).then(() => {
            showCopySuccess(btn);
        }).catch(err => {
            console.error('Failed to copy text: ', err);
            fallbackCopyTextToClipboard(decodedText, btn);
        });
    } else {
        // Fallback for older browsers
        fallbackCopyTextToClipboard(decodedText, btn);
    }
}

// Fallback copy method for older browsers
function fallbackCopyTextToClipboard(text, btn) {
    const textArea = document.createElement("textarea");
    textArea.value = text;

    // Avoid scrolling to bottom
    textArea.style.top = "0";
    textArea.style.left = "0";
    textArea.style.position = "fixed";
    textArea.style.opacity = "0";

    document.body.appendChild(textArea);
    textArea.focus();
    textArea.select();

    try {
        const successful = document.execCommand('copy');
        if (successful) {
            showCopySuccess(btn);
        } else {
            console.error('Fallback: Could not copy text');
        }
    } catch (err) {
        console.error('Fallback: Could not copy text: ', err);
    }

    document.body.removeChild(textArea);
}

// Show copy success feedback
function showCopySuccess(btn) {
    const originalText = btn.innerHTML;
    const originalClass = btn.className;

    // Change button appearance
    btn.innerHTML = '<i class="fas fa-check"></i> Copied!';
    btn.classList.add('copied');

    // Reset after 2 seconds
    setTimeout(() => {
        btn.innerHTML = originalText;
        btn.className = originalClass;
    }, 2000);
}

// Decode HTML entities
function decodeHtmlEntities(text) {
    const textArea = document.createElement('textarea');
    textArea.innerHTML = text;
    return textArea.value;
}

// Export chat history to CSV
function exportChatHistory() {
    if (!chatHistory || chatHistory.length === 0) {
        alert('No chat history to export!');
        return;
    }

    // Get all unique models from chat history
    const allModels = new Set();
    chatHistory.forEach(entry => {
        if (entry.responses) {
            entry.responses.forEach(response => {
                allModels.add(response.model || response.model_id);
            });
        }
    });

    const modelsList = Array.from(allModels).sort();

    // Create CSV header
    let csvContent = 'No.,Question';
    for (let i = 0; i < 5; i++) {
        const modelName = modelsList[i] || `Model ${i + 1}`;
        csvContent += `,${escapeCSV(modelName)}`;
    }
    csvContent += '\n';

    // Add data rows
    chatHistory.forEach((entry, index) => {
        const stt = index + 1;
        const question = entry.user_message || '';

        // Create responses map for easy lookup
        const responsesMap = {};
        if (entry.responses) {
            entry.responses.forEach(response => {
                const modelName = response.model || response.model_id;
                const rawText = response.raw_response || response.response || '';
                // Clean markdown and HTML from response
                const cleanText = cleanTextForCSV(rawText);
                responsesMap[modelName] = cleanText;
            });
        }

        // Build CSV row - separate No. and Question into different cells
        let row = `${stt},${escapeCSV(question)}`;
        for (let i = 0; i < 5; i++) {
            const modelName = modelsList[i];
            const response = modelName ? (responsesMap[modelName] || '') : '';
            row += `,${escapeCSV(response)}`;
        }
        row += '\n';

        csvContent += row;
    });

    // Create filename with session ID and date
    const currentDate = new Date().toISOString().split('T')[0];
    const sessionIdShort = sessionId ? sessionId.substring(0, 8) : 'unknown';
    const filename = `chat_history_${currentDate}_session_${sessionIdShort}.csv`;

    // Create and download file
    downloadCSV(csvContent, filename);
}

// Clean text for CSV (remove markdown and HTML)
function cleanTextForCSV(text) {
    if (!text) return '';

    // Remove HTML tags
    let cleaned = text.replace(/<[^>]*>/g, '');

    // Remove markdown formatting
    cleaned = cleaned
        .replace(/\*\*(.*?)\*\*/g, '$1') // Bold
        .replace(/\*(.*?)\*/g, '$1')     // Italic
        .replace(/`(.*?)`/g, '$1')       // Code
        .replace(/#{1,6}\s/g, '')        // Headers
        .replace(/>\s/g, '')             // Quotes
        .replace(/\n\s*\n/g, '\n')       // Multiple newlines
        .replace(/\n/g, ' ')             // Single newlines to space
        .trim();

    return cleaned;
}

// Escape CSV values
function escapeCSV(text) {
    if (!text) return '';

    // Convert to string and escape quotes
    let escaped = String(text).replace(/"/g, '""');

    // Wrap in quotes if contains comma, quote, or newline
    if (escaped.includes(',') || escaped.includes('"') || escaped.includes('\n')) {
        escaped = `"${escaped}"`;
    }

    return escaped;
}

// Download CSV file
function downloadCSV(csvContent, filename) {
    // Add BOM for UTF-8 encoding
    const BOM = '\uFEFF';
    const csvData = BOM + csvContent;

    // Create blob and download
    const blob = new Blob([csvData], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');

    if (link.download !== undefined) {
        // Modern browsers
        const url = URL.createObjectURL(blob);
        link.setAttribute('href', url);
        link.setAttribute('download', filename);
        link.style.visibility = 'hidden';
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        URL.revokeObjectURL(url);

        // Show success message
        showSuccessMessage(`Chat history exported to ${filename}`);
    } else {
        // Fallback for older browsers
        alert('Your browser does not support file downloads. Please use a modern browser.');
    }
}

// Make functions available globally
window.resetSession = resetSession;
window.copyResponse = copyResponse;
window.exportChatHistory = exportChatHistory;

// Add typing indicator simulation
let typingTimeout;
messageInput.addEventListener('input', function() {
    clearTimeout(typingTimeout);

    // Show typing indicator (could be enhanced)
    typingTimeout = setTimeout(() => {
        // Hide typing indicator
    }, 1000);
});
