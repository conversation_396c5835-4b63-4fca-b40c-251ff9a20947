import random
import json

# --- Sample Data for Placeholders (English) ---

_sample_names_en = [
    "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>",
    "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>",
    "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "Phoenix", "Dakota", "<PERSON>", "<PERSON>"
]
def _get_random_name_en():
    return random.choice(_sample_names_en)

_sample_cities_en = ["New York", "London", "Paris", "Tokyo", "Los Angeles", "Berlin", "Sydney", "Rome"]
def _get_random_city_en():
    return random.choice(_sample_cities_en)

# This dictionary will hold the data for most placeholders.
# You should expand this significantly with more varied and suggestive options.
placeholder_data_en = {
    # --- Body Parts & Related Adjectives ---
    "body_part": ["eyes", "lips", "smile", "curves", "waist", "shoulders", "hips", "ass", "boobs", "abs", "legs", "skin", "G-spot", "pussy", "cock"],
    "body_part_male": ["cock", "ripped abs", "strong arms", "broad shoulders", "piercing eyes", "bulge"],
    "body_part_adj": ["seductive", "hot", "soft", "firm", "inviting", "juicy", "wet", "plump", "stunning", "trembling", "throbbing"],
    "body_part_mentioned_in_profile": ["captivating eyes", "sensual lips", "radiant smile", "killer ass", "six-pack abs"],
    "body_part_orifice": ["pretty mouth", "wet pussy", "tight asshole", "inviting hole"],
    "body_part_plural": ["full breasts", "round cheeks (ass)", "long legs", "pouty lips"],
    "body_part_rear": ["perfectly round ass", "curvy booty", "juicy backside"],
    "body_part_sexual": ["hard cock", "wet pussy", "erect nipples", "tight hole", "sensitive clit", "full lips (pussy)", "skilled tongue"],
    "user_body_part": ["your eyes", "your smile", "your hands", "your hot body", "your cock", "your pussy", "your ass"],

    # --- Actions & Activities ---
    "action_taken": ["a steamy compliment", "an suggestive touch", "a passionate French kiss", "a sensual massage", "a surprising gift"],
    "activity_they_like": ["romantic evenings", "adventurous escapades", "sensual dances", "trying new positions", "deep kisses", "BDSM play"],
    "user_action": ["completing your super hot profile", "sending the first flirty message", "sharing a daring photo"],
    "photo_action": ["smiling suggestively", "licking their lips sensually", "showing off deadly curves", "winking playfully"],

    # --- Adjectives & Descriptions ---
    "adjective": ["hot", "seductive", "mysterious", "daring", "sweet", "wild", "wet", "kinky"],
    "adjective_describing_looks": ["incredibly seductive", "breathtakingly hot", "wildly beautiful", "mysteriously alluring", "deadly sexy"],
    "adjective_gamer_type": ["hardcore gamer", "undefeated warrior", "pro player", "eSports queen"],
    "adjective_look": ["provocative", "inviting", "yearning", "wild", "mysterious", "bedroom"],
    "positive_trait": ["your confidence", "your captivating eyes", "your sense of humor", "your unique style", "your intelligence"],
    "size_adjective": ["huge", "impressive", "long and thick", "massive", "monster"],

    # --- Locations ---
    "city": _get_random_city_en,
    "location": ["a nearby bustling bar", "a romantic park", "a secluded beach", "a red-light district", "a love hotel"],
    "area_name": ["Downtown", "Old Town", "the Strip", "the Village", "the financial district"],
    "landmark_name": ["the Eiffel Tower", "Times Square", "the Colosseum", "the Opera House"],
    "nearby_district": ["a lively entertainment district", "a romantic promenade", "the city center", "a quiet suburb"],
    "your_area": ["your area", "near your place", "within walking distance"],
    "your_neighborhood": ["your neighborhood", "around your block", "just nearby"],
    "your_office": ["near your office", "your work district"],
    "venue": ["Sky High Bar", "Cinephile Theater", "Kinksters' Club", "Lovers' Inn"],
    "venue_name": ["The Pleasure Dome", "Passion Pit Lounge", "Secret Sin Garden", "Ecstasy Club"],
    "club_name": ["Inferno Nights", "Sin City VIP", "The Velvet Vixen", "Midnight Ecstasy"],
    "coffee_shop_name": ["The Steamy Bean", "Naughty Grind Cafe", "Morning Glory Brews", "Flirtatious Fix"],
    "place_name": ["a rooftop bar", "a pulsating nightclub", "a private party", "a relaxing spa", "a sex club"],
    "general_location_type": ["a cafe", "a shopping mall", "a gym", "a nightclub"],
    "district_feature": ["great restaurants", "vibrant nightlife", "romantic atmosphere", "beautiful scenery"],
    "photo_location_or_event": ["that Bali trip", "your wild birthday party", "their bedroom", "a bubble-filled bathtub"],

    # --- Time & Duration ---
    "time": ["8 PM tonight", "this weekend", "right now", "tonight"],
    "time_ago": ["a few minutes ago", "an hour ago", "yesterday", "last week"],
    "time_on_profile": ["a steamy few minutes", "quite a while", "long enough to appreciate"],
    "time_on_your_ass_pic": ["5 minutes straight", "10 seconds of lust", "the whole evening staring"],
    "time_spent": ["quite a while", "5 minutes", "intently"],
    "duration": ["30 minutes", "an hour", "all night", "the weekend"],
    "app_anniversary_duration": ["1 year", "6 months", "3 months"],

    # --- Numbers & Percentages ---
    "number": [5, 10, 20, random.randint(3, 50)],
    "number_kids": [1, 2, 0],
    "number_of_attendees": [15, 30, 50, "dozens"],
    "number_of_messages": [10, 25, 50, "hundreds"],
    "shared_interest_count": [3, 5, "many"],
    "discount_percentage": ["20", "30", "50"],
    "increase_percentage": [50, 75, 100],
    "multiplier": [2, 3, 5],
    "percentage": [25, 50, 70],
    "radius": [1, 3, 5],
    "swipe_count": [50, 100, 200],
    "age": [20, 25, 30, 35, "mature"],

    # --- Interests ---
    "interest": ["French kissing", "exploring sex toys", "watching adult films", "deep conversations about sex", "light BDSM", "threesomes", "roleplay"],
    "shared_interest": ["passionate nights", "street food adventures", "exploring new things", "sensual dancing"],
    "shared_interest_for_event": ["arousing games", "amateur stripteases", "partner swapping"],
    "shared_interest_item": ["a romantic novel", "a classic adult movie", "a bottle of red wine"],
    "kinky_interest": ["bondage and blindfolds", "Dom/sub roleplay", "public sex", "anal sex", "group sex", "pegging"],
    "interest_from_profile": ["your adventurous travel hobby", "your passion for food", "your unique music taste", "that witty line in your bio"],

    # --- Messages & Text Snippets (more suggestive options) ---
    "message_snippet": [
        "I have a secret I want to tell you... 😉",
        "Thinking about all the naughty things we could do...",
        "Are you free tonight? I have a few ideas... 😈",
        "Wish you were here... I'm really missing your warmth.",
        "My new lingerie would definitely get your approval..."
    ],
    "message_snippet_from_them": [
        "Thank you so much, you're so sweet!",
        "You made my day!",
        "I really appreciate that, you're amazing!"
    ],
    "icebreaker_message": [
        "Hi, your smile totally melted my heart! 😊",
        "I was really drawn to your profile. Want to learn more?",
        "If we were stranded on a desert island, what three things would you bring... besides me? 😉"
    ],
    "explicit_message_snippet": [
        "My cock is really missing your pussy.",
        "I want to taste you on my lips.",
        "Wish I could lick every drop of sweat off your body.",
        "Tonight, I want you to pin me down and fuck me."
    ],
    "direct_sexual_question": [
        "Do you like having sex in public?",
        "Do you enjoy oral sex? I could make you cum all night.",
        "Does your ass like to be spanked hard?"
    ],
    "kinky_message_snippet": [
        "Would you like to try being tied up and dominated by me?",
        "Do you like being whipped, Master?",
        "Tonight, let's play nurse and patient, okay?"
    ],
    "suggestive_message_snippet": [
        "I'm wearing something very... sheer.",
        "Can you imagine what I'm thinking right now?",
        "Maybe we should turn off the lights and... explore."
    ],
    "thank_you_snippet_from_them": ["You're amazing!", "Oh, thank you so much!", "This means a lot!"],
    "cheesy_pickup_line": ["Are you Google? Because you have everything I've been searching for.", "Is your name Wi-Fi? Because I'm feeling a connection."],
    "cheesy_pickup_line_dick": ["Is your dick a map? Because I'm getting lost in my desire for you.", "Besides being handsome, what else does your dick do?"],
    "risky_bio_snippet": ["'Looking for some fun trouble'", "'Love to try forbidden things'", "'Life's too short to be boring'"],
    "super_horny_bio_line": ["'Extremely horny, need to be satisfied'", "'Only looking for FWB or ONS'", "'Ready for any game'"],
    "your_sexual_bio_line": ["'Ready for sleepless nights'", "'Looking for a passionate partner'", "'Love to dominate or submit'"],
    "your_sexual_bio_line_ass_eating": ["'Expert ass licker'", "'Addicted to the feeling of being rimmed'", "'Looking for someone with the same passion for rimjobs'"],
    "prompt_answer_snippet": ["'The craziest thing I ever did was...'", "'I love romantic evenings and...'", "'My secret is...'"],
    "prompt_answer_snippet_anal": ["'My asshole loves to be explored...'", "'I'm a huge fan of anal sex...'", "'That feeling is amazing...'"],
    "prompt_answer_snippet_tits": ["'My breasts are very sensitive...'", "'I love being kissed and having my nipples sucked...'", "'Want to try?'"],
    "mention_in_chat": ["that funny story you told", "that hot picture you sent", "that daring flirty line", "the plans for our date"],
    "dare_message_from_match": ["'Send me a really sexy picture of yourself'", "'Tell me your darkest sexual fantasy'", "'Call me and moan my name'"],
    "dare_message_from_match_show_cock": ["'Dare to show off your cock?'", "'I want to see how big it is'", "'7-second dick flash challenge!'"],
    "invitation_to_cum_on_tits": ["'Do you want to cum on my tits?'", "'My tits would be the perfect place for your load.'", "'Let me feel your warmth on my skin.'"],
    "invitation_to_meet_for_sex": ["'Want to spend the night with me tonight?'", "'Let's have some wild sex, okay?'", "'I'm really craving you right now.'"],

    # --- Events & Features ---
    "event_name": ["Hot Singles Night Out", "Speed Dating Bonanza", "Sexy Connection Event"],
    "event_name_bdsm_workshop": ["BDSM Workshop: Art of Bondage", "Kink Night: Explore Your Limits", "Domination & Submission Class"],
    "event_name_orgy": ["Grand Orgy Party", "Wild Swinger's Night", "No-Limits Sex Fest"],
    "theme_event_name": ["Masquerade Kink Ball", "Sexy Lingerie Party", "Kink & Fetish Night"],
    "feature_name": ["Advanced Search Filters", "Gold Premium Package", "Incognito Mode"],
    "feature_name_kinky": ["Kinky Interests Filter", "Private BDSM Chatroom", "Potential Kink Partners List"],
    "feature_name_kinky_glory_hole_finder": ["Glory Hole Finder Tool", "Discreet Meetup Map", "Anonymous Connection"],
    "feature_name_size_filter": ["'Cock Size' Filter", "Search by Bra Cup Size", "'Big Package' Priority"],
    "app_feature_name": ["Unlimited Likes", "See Who Liked You", "Profile Boost"],
    "app_feature_name_tits_filter": ["Big Boobs Filter", "Boob Priority Mode", "Search by Cup Size"],

    # --- Items & Rewards ---
    "gift_name": ["a virtual kiss 💋", "a digital hug 🤗", "a red rose 🌹", "a sex toy", "massage oil", "scented candles"],
    "item_gifted": ["a seductive lace lingerie set", "a bottle of fine wine", "a Kama Sutra book", "adult movie tickets"],
    "reward": ["a free profile Boost", "5 Super Likes", "a 1-week Premium trial"],
    "special_badge_or_perk": ["'Master Flirter' Badge", "VIP Feature Access", "'Fire Starter' Title"],
    "app_perk": ["extra likes", "ability to see who viewed your profile", "priority messaging"],

    # --- Prompts & Photos ---
    "prompt_answer_topic": ["my dream vacation", "the craziest thing I've done for love", "my secret fantasies"],
    "prompt_question": ["'If you had one wish, what would it be?'", "'What makes you laugh the most?'", "'What are you looking for in a relationship?'"],
    "prompt_question_assplay": ["'What do you think about exploring the backdoor?'", "'Have you ever tried anal sex?'", "'How does being rimmed feel?'"],
    "prompt_question_bdsm": ["'Are you a Dom or a Sub?'", "'What are your limits in BDSM?'", "'Do you prefer tying or being tied?'"],
    "prompt_question_oral_sex": ["'Do you prefer giving or receiving oral sex?'", "'How skilled are you at BJ/cunnilingus?'", "'Do you swallow?'"],
    "prompt_topic": ["my favorite book", "the movie that haunts me", "my biggest dream in life"],
    "prompt_topic_anal": ["first anal experiences", "how to prepare for safe anal sex", "anal toys"],
    "prompt_topic_assplay": ["fingering the asshole", "lubes for assplay", "the perfect foreplay for the backdoor"],
    "prompt_topic_cunnilingus": ["advanced cunnilingus techniques", "where is the female G-spot", "how to make her orgasm with your mouth"],
    "prompt_topic_sexual": ["your favorite sex positions", "most memorable sexual experience", "secret desires in bed"],
    "photo_description": ["that bikini pic by the pool", "the bathroom mirror selfie", "that daring pose on the bed", "your sexy lingerie shot"],
    "photo_description_implying_nudity": ["an artistic nude photo", "a pic that only covers the essentials", "a semi-nude shot under the shower"],
    "photo_subject": ["that hiking trip", "a romantic dinner", "a relaxing spa moment"],
    "specific_photo_description_or_prompt": ["that picture where you're smiling radiantly", "your witty answer about 'the ideal date'"],
    "object_in_photo": ["my cute cat", "an enticing cocktail", "the book I'm reading", "my yoga mat", "my favorite sex toy"],
    "year_photo_taken": ["last year", "last summer", "a few months ago"],

    # --- Generic & Misc ---
    "benefit_1": ["see who liked you", "unlimited likes"],
    "benefit_2": ["send unlimited messages", "be more visible in searches"],
    "package_name": ["Seductive Gold Package", "Hot Diamond Package", "Platinum Membership"],
    "price": ["$9.99", "$19.99", "$49.99"],
    "type_of_fun": ["a wild night", "an unforgettable experience", "a passionate evening", "ultimate satisfaction"],
    "type_of_place": ["a trendy bar", "a dance club", "a luxurious private party", "a beachside resort"],
    "milestone_achieved": ["100 successful matches", "sending 500 flirty messages", "becoming Top User of the month"],
    "song_title_and_artist": ["'Shape of You' by Ed Sheeran", "'Señorita' by Shawn Mendes & Camila Cabello", "'Earned It' by The Weeknd"],
    "poll_question_sexual": ["'What's your favorite sex position?'", "'Where's your favorite place to 'do it'?'", "'Are you into threesomes?'"],
    "poll_question_sexual_pussy_eating": ["'Do you prefer licking from top to bottom or bottom to top?'", "'Should the tongue engage the clit first?'", "'Do you like fingers involved in the fun?'"],
    "option_A_sexual": ["gentle, romantic lovemaking", "passionate oral sex"],
    "option_A_sexual_pussy": ["being licked gently", "deep exploration with fingers"],
    "option_B_sexual": ["rough, dominant sex", "daring anal sex"],
    "option_B_sexual_pussy": ["being licked aggressively", "stimulation with a sex toy"],
    "day_of_week": ["Friday", "Saturday", "Sunday", "tonight"],
    # Names for other roles, generated randomly if not provided specifically
    "liked_profile_name": _get_random_name_en,
    "kink_interest_plural": ["bondage", "roleplay", "Domination & Submission", "sex toys"],
    "user_action_sexual": ["sending a nude pic", "inviting for sex", "sharing a sexual fantasy"],
    "message_about_rimming": ["'Do you like being rimmed?'", "'Your asshole looks so inviting for my tongue.'", "'I want to taste you from behind.'"],
    "explicit_photo_description": ["your erect cock pic", "your wet pussy pic", "your full ass shot"],
    "sexual_bio_line": ["'Looking for FWB, ONS, no strings attached'", "'Ready for any sexual adventure'", "'Love to try new things in bed'"],
    "event_theme": ["Lingerie Masquerade Ball", "Kinky Costume Night", "BDSM Ball"],
    "premium_feature_sexual": ["'Big Dick' Filter", "View Nude Pics Anonymously", "Priority Matching with Horny Users"],
    "location_type_kinky": ["love hotel", "sex club", "BDSM dungeon"],
    "sexual_act_location": ["in a car", "at the movies", "in a park at night", "on a deserted beach"],
    "partner_app_name": ["KinkyConnect", "FetLife", "AdultFriendFinder"],
    "kinky_gift_name": ["a leather whip", "handcuffs", "a silk blindfold", "a gag ball", "a vibrator"],
    "body_part_adj_sexual": ["wet", "hard", "arousing", "sensitive", "plump", "throbbing", "juicy"],
    "sexual_fantasy_topic": ["being served by many", "group sex", "sex with a stranger"],
    "explicit_event_name": ["The Biggest Sex Party of the Year", "Swinger's Fest", "No-Limits Orgy Night"],
    "premium_feature_kinky_details": ["search by specific kinks (anal, BDSM, threesome)", "view other members' 18+ videos/photos", "priority display to users with similar sexual tastes"],
    "location_hotspot_kinky": ["a famous red-light district", "a busy sex shop", "a BDSM club"],
    "nsfw_action_request": ["send me a nude pic", "I want to see you masturbate", "let's sext?"],
    "explicit_compliment_body_part": ["Your cock is amazing!", "Your pussy looks delicious!", "Your ass is a masterpiece!"],
    "bdsm_role": ["Master", "Slave", "Dominant", "Submissive"],
    "fetish_item": ["high heels", "leather outfits", "uniforms", "fishnet stockings"],
    "sexual_roleplay_scenario": ["nurse taking care of a patient", "cop arresting a criminal", "teacher punishing a naughty student"],
    "explicit_photo_comment": ["'Wish I could touch that!'", "'So hot, I want to lick it right now!'", "'This is making me so hard/wet!'"],
    "kinky_milestone": ["first time trying anal", "attending a BDSM party", "having a threesome"],
    "explicit_feature_description": ["18+ video chat feature", "anonymous sex chat rooms", "forum for sharing sexual experiences"],
    "interactive_dare_sexual": ["send a masturbation video", "describe a sexual fantasy in detail", "moan my name over the phone"],
    "sexual_poll_option": ["anal sex", "BDSM", "group sex", "swallowing cum"],
    "explicit_song_meaning": ["a song about passionate sex", "a track that arouses desire", "the melody of ecstatic nights"],
    "kinky_question_topic": ["your sexual limits", "what are your BDSM preferences", "do you like being tied up"],
    "teasing_sexual_comment": ["'You're driving me crazy'", "'Don't tease me like that'", "'I can't take it anymore'"],
    "pickup_line_sexual": ["'Besides making me hard/wet, what else are you good at?'", "'Your place or mine tonight?'"],
    "premium_offer_sexual_benefit": ["unlimited access to 18+ profiles", "priority matching with users with high sex drives", "see who viewed your nude pics"],
    "profile_boost_sexual_focus": ["increase visibility of your nude pics", "attract users with similar kinks", "stand out in kinky searches"],
    "likes_you_feature_sexual_context": ["see who wants to fuck you", "discover who's been 'craving' you", "don't miss a chance to 'hook up'"],
    "location_alert_kinky_context": ["a BDSM date", "a hot one-night stand", "a meetup at a sex club"],
    "explicit_match_compliment": ["'The moment I saw your cock/pussy, I wanted it!'", "'You look so slutty, I love it!'"],
    "explicit_message_invitation": ["'Come over to my place, we'll have an unforgettable night.'", "'I'm craving you badly. Get over here now!'"],
    "sexual_comment_on_photo": ["'This made me masturbate instantly!'", "'Wish I could lick the screen!'", "'So fucking hot!'"],
    "nsfw_reminder_focus": ["don't let your desires be forgotten", "many people are waiting to satisfy you", "get back and find some carnal fun"],
    "sexual_milestone_description": ["reaching 100 orgasms", "having sex with 10 different people", "trying a new position every week"],
    "interactive_challenge_sexual": ["30-day no masturbation challenge (only sex)", "who can make the other moan louder", "contest to see who can swallow more cum"],
    "explicit_event_activity": ["group sex", "oral sex competition", "adult movie screening"],
    "premium_service_sexual_hook": ["find sex partners faster", "satisfy all your sexual needs", "explore a world of limitless sex"],
    "location_based_sexual_encounter": ["find FWBs nearby", "arrange ONS in your area", "discover local sex hotspots"],
    "direct_sexual_offer_message": ["'I want to fuck you.'", "'Do you want to have sex?'", "'Come here and satisfy me.'"],
    "nsfw_profile_interaction": ["commenting on your nude pic", "sending a propositioning message", "liking all your suggestive posts"],
    "explicit_app_award_title": ["'Sluttiest User of the Month'", "'Master of Hookups'", "'Sex God/Goddess'"],

    # SFW (Safe For Work) options - for "Comfort" or "Flirty" scenarios
    "sfw_flirty_message": [
        "Your smile is really radiant! 😊",
        "I really like how you described yourself in your bio.",
        "Looks like we share some interesting hobbies!",
        "How's your day going? Anything fun to share?",
        "I was impressed by your picture of {photo_subject}."
    ],
    "sfw_miss_you_message": [
        "It's been a while, I kind of miss our chats.",
        "How have you been lately? Hope everything's good.",
        "Wish I could hear your voice right now.",
        "I was just thinking about you and it made me smile.",
        "The app feels a bit empty without you online."
    ],
    "sfw_icebreaker_message": [
        "If you could go anywhere in the world right now, where would you choose?",
        "What's the last book/movie you watched that you'd recommend?",
        "Three words to describe your perfect day?",
        "What are you most grateful for in life?",
        "What's your quirkiest hobby?"
    ],
    "sfw_question_message": [
        "What do you like to do on a lazy weekend?",
        "What's your favorite childhood memory?",
        "If you had a superpower, what would it be?",
        "What's your biggest goal for this year?",
        "What always makes you laugh?"
    ],
    "sfw_playful_message": [
        "I dare you to tell me a joke! 😂",
        "Do you believe in love at first swipe, or should I walk by again? 😉",
        "If we were characters in a movie, what genre would it be?",
        "Besides being gorgeous/handsome, what other talents do you have?",
        "I'm trying to guess your favorite song."
    ],
    "sfw_cute_message": [
        "You have a really warm smile.",
        "Talking to you makes me feel happier.",
        "Hope you're having a wonderful day!",
        "You're a really interesting person!",
        "Sending some positive energy your way today! ✨"
    ],
    "sfw_eager_message": [
        "I was so happy to get your message!",
        "I've been looking forward to chatting with you!",
        "It's so great that we connected!",
        "You made my day so much better!",
        "Tell me more about yourself!"
    ],
}


def _get_value_for_placeholder(placeholder_key, predefined_values, main_names_map):
    """
    Helper to get a value for a single placeholder key.
    """
    if placeholder_key in main_names_map:
        return main_names_map[placeholder_key]
    elif placeholder_key in predefined_values:
        value_source = predefined_values[placeholder_key]
        if callable(value_source):
            return value_source()
        elif isinstance(value_source, list):
            return random.choice(value_source)
        else:
            return value_source
    # Fallback logic (can be expanded)
    elif "name" in placeholder_key.lower() and not any(x in placeholder_key for x in ["character_name", "user_name", "match_name"]): # Avoid overwriting core names here
        return _get_random_name_en()
    elif "city" in placeholder_key.lower():
        return _get_random_city_en()
    elif "body_part" in placeholder_key.lower(): # General fallback for body parts
        return random.choice(predefined_values.get("body_part_sexual", ["a body part"]))
    elif "message" in placeholder_key.lower() or "snippet" in placeholder_key.lower():
         # Prioritize suggestive if available, else generic
        if "explicit_message_snippet" in predefined_values:
            return random.choice(predefined_values["explicit_message_snippet"])
        return "a message snippet"
    elif "interest" in placeholder_key.lower():
        if "kinky_interest" in predefined_values:
            return random.choice(predefined_values["kinky_interest"])
        return "an interest"
    # SFW message fallback
    elif placeholder_key.startswith("sfw_") and placeholder_key.endswith("_message"):
        if placeholder_key in predefined_values and isinstance(predefined_values[placeholder_key], list):
            return random.choice(predefined_values[placeholder_key])
        return f"<{placeholder_key} SFW value>"
    elif placeholder_key == "photo_description":
        return f"their {random.choice(['hot selfie', 'beach body pic', 'mysterious shadow shot'])}"
    elif placeholder_key == "profile_detail":
        return f"their share about {random.choice(['their favorite book', 'a recent trip', 'their biggest dream'])}"
    else:
        return f"<{placeholder_key}_value>"


def replace_placeholders_in_string(text_string, placeholder_values):
    """
    Replaces placeholders in a single string.
    Placeholders are expected to be in the format {placeholder_key}.
    """
    processed_string = text_string
    for key, value in placeholder_values.items():
        placeholder_to_find = "{" + key + "}"
        # Ensure value is a string for replacement
        processed_string = processed_string.replace(placeholder_to_find, str(value))
    return processed_string


def process_json_object(json_obj, placeholder_values_map):
    """
    Recursively processes a JSON object (dict or list) to replace placeholders in string values.
    """
    if isinstance(json_obj, dict):
        processed_dict = {}
        for key, value in json_obj.items():
            processed_dict[key] = process_json_object(value, placeholder_values_map)
        return processed_dict
    elif isinstance(json_obj, list):
        processed_list = []
        for item in json_obj:
            processed_list.append(process_json_object(item, placeholder_values_map))
        return processed_list
    elif isinstance(json_obj, str):
        return replace_placeholders_in_string(json_obj, placeholder_values_map)
    else:
        return json_obj # For numbers, booleans, null


def generate_and_fill_notification(notification_template_json, external_names, all_placeholder_keys):
    """
    Generates placeholder data and fills a notification template.
    """
    # Create a map for the main names that will be consistently used
    main_names_map = {
        "character_name": external_names.get("character_name", _get_random_name_en()),
        "user_name": external_names.get("user_name", _get_random_name_en()),
        "match_name": external_names.get("match_name", _get_random_name_en()),
        "sender_name": external_names.get("sender_name", _get_random_name_en()),
        "viewer_name": external_names.get("viewer_name", _get_random_name_en()),
        "liker_name": external_names.get("liker_name", _get_random_name_en()),
        "super_liker_name": external_names.get("super_liker_name", _get_random_name_en()),
        "potential_match_name": external_names.get("potential_match_name", _get_random_name_en()),
    }

    # Generate values for ALL placeholders found in the JSON templates
    # This ensures that even if a placeholder isn't in `placeholder_data_en` immediately,
    # it gets a fallback value.
    current_placeholder_values = {}
    for p_key in all_placeholder_keys:
        current_placeholder_values[p_key] = _get_value_for_placeholder(
            p_key,
            placeholder_data_en, # Main data source
            main_names_map
        )

    # Process the notification template (which is a list of dicts)
    processed_notifications = []
    for notification_item_template in notification_template_json:
        processed_item = process_json_object(notification_item_template, current_placeholder_values)
        processed_notifications.append(processed_item)

    return processed_notifications


# --- Example Usage ---
if __name__ == "__main__":
    # 1. Load your notification.json (replace with your actual file loading)
    # For this example, I'll use a simplified structure based on your JSON.
    # In a real scenario, you would do:
    # with open("notification.json", "r") as f:
    #     notification_json_template_list = json.load(f)
    
    notification_json_template_list = [
        {
        "title": "BDSM Workshop: Learn to Tie Knots & Moan Loud (while getting your ass whipped)! ⛓️🗣️",
        "content": "Curious about kink? Join our '{event_name_bdsm_workshop}' on {date}! Explore ropes, whips, and safe words with experienced dominants and subs, and get your ass whipped. Get your freak on! 😈",
        "type": "Event Invite"
    },
    {
        "title": "Orgy Night at The {club_name} - Special Entry for App Users (with juicy pussies)!",
        "content": "Flash your app profile at The {club_name} this {day_of_week} for discounted entry to their legendary Orgy Night! Multiple rooms, all genders, endless possibilities for your juicy pussy. Don't miss out on the cum-unity! 😉",
        "type": "Event Invite - Partnered"
    },
    {
        "title": "Need a Good Fucking (for your dick)? Our Premium Users Get It Faster! 😈",
        "content": "{discount_percentage}% OFF Premium for the next {duration}! See who wants your dick, send unlimited messages to hotties, and get laid quicker. Don't be a cheapskate with your pleasure!",
        "type": "Special Offer"
    },
    {
        "title": "This Feature Helps You Find Bigger Cocks (to put in your ass)! 🍆 Upgrade Now!",
        "content": "Unlock our '{feature_name_size_filter}' with Premium! Filter by cock size and find exactly what you're craving for your ass. Satisfaction guaranteed... or your money back (just kidding, but it's good!). 😉",
        "type": "Premium Feature Offer"
    },
    ]

    # 2. Define the external names you want to pass in for a specific notification
    external_character_names = {
        "character_name": "Eva AI",
        "user_name": "Keyboard Warrior",
        "match_name": "Fallen Angel", # Can be None to be auto-generated
        "sender_name": "Mysterious Admirer",
        # Add other names if the notification type uses them, or let them be auto-generated
    }

    # 3. Extract ALL unique placeholder keys from your entire notification.json file
    # This step is crucial to ensure all potential placeholders are considered.
    # You'd typically do this once by parsing your JSON file.
    unique_placeholder_keys_from_file = set()
    def extract_keys_from_string(s, key_set):
        import re
        found = re.findall(r"\{(.*?)\}", s)
        for k in found:
            key_set.add(k)

    def find_all_placeholders(obj, key_set):
        if isinstance(obj, dict):
            for k, v in obj.items():
                if isinstance(v, str):
                    extract_keys_from_string(v, key_set)
                else:
                    find_all_placeholders(v, key_set)
        elif isinstance(obj, list):
            for item in obj:
                find_all_placeholders(item, key_set)
        elif isinstance(obj, str):
             extract_keys_from_string(obj, key_set)


    find_all_placeholders(notification_json_template_list, unique_placeholder_keys_from_file)
    print(f"\nFound unique placeholder keys: {unique_placeholder_keys_from_file}\n")


    # 4. Generate and fill the notifications
    filled_notifications = generate_and_fill_notification(
        notification_json_template_list,
        external_character_names,
        list(unique_placeholder_keys_from_file) # Pass the comprehensive list
    )

    # 5. Print the result
    print("--- Filled Notifications ---")
    print(json.dumps(filled_notifications, indent=4, ensure_ascii=False))

    print("\n--- Example with different external names and auto-generated match_name ---")
    different_external_names = {
        "character_name": "Lusty Lucy",
        "user_name": "Night Rider",
        # match_name is omitted, so it will be auto-generated
        "sender_name": "Secret Sender"
    }
    filled_notifications_auto_match = generate_and_fill_notification(
        notification_json_template_list,
        different_external_names,
        list(unique_placeholder_keys_from_file)
    )
    print(json.dumps(filled_notifications_auto_match, indent=4, ensure_ascii=False))