2025-08-12 07:35:13.705 | INFO     | chatbot.entrypoint.api:get_image_endpoint:272 - Get_image request for user partnr-development-299 processed with 0 attempts
2025-08-12 07:35:26.602 | INFO     | chatbot.entrypoint.router.soulplay_button:describe_message:83 - Start describe message...
2025-08-12 07:35:26.608 | INFO     | chatbot.entrypoint.router.soulplay_button:describe_message:94 - Start get history memory...
2025-08-12 07:35:26.654 | INFO     | chatbot.ai.llm_session_manage:load_lasted_history:106 - Create history less than 10 times
2025-08-12 07:35:26.686 | ERROR    | chatbot.entrypoint.router.soulplay_button:describe_message:165 - Error in description_message endpoint: 'NoneType' object is not subscriptable
2025-08-12 07:35:26.687 | ERROR    | chatbot.entrypoint.router.soulplay_button:describe_message:166 - 'NoneType' object is not subscriptable
Traceback (most recent call last):

  File "/usr/local/bin/uvicorn", line 10, in <module>
    sys.exit(main())
    │   │    └ <Command main>
    │   └ <built-in function exit>
    └ <module 'sys' (built-in)>
  File "/usr/local/lib/python3.10/site-packages/click/core.py", line 1442, in __call__
    return self.main(*args, **kwargs)
           │    │     │       └ {}
           │    │     └ ()
           │    └ <function Command.main at 0x799ab3971090>
           └ <Command main>
  File "/usr/local/lib/python3.10/site-packages/click/core.py", line 1363, in main
    rv = self.invoke(ctx)
         │    │      └ <click.core.Context object at 0x799ab46383d0>
         │    └ <function Command.invoke at 0x799ab3970f70>
         └ <Command main>
  File "/usr/local/lib/python3.10/site-packages/click/core.py", line 1226, in invoke
    return ctx.invoke(self.callback, **ctx.params)
           │   │      │    │           │   └ {'host': '0.0.0.0', 'workers': 1, 'port': 8001, 'app': 'chatbot.entrypoint.api:app', 'uds': None, 'fd': None, 'reload': False...
           │   │      │    │           └ <click.core.Context object at 0x799ab46383d0>
           │   │      │    └ <function main at 0x799ab3868280>
           │   │      └ <Command main>
           │   └ <function Context.invoke at 0x799ab3970310>
           └ <click.core.Context object at 0x799ab46383d0>
  File "/usr/local/lib/python3.10/site-packages/click/core.py", line 794, in invoke
    return callback(*args, **kwargs)
           │         │       └ {'host': '0.0.0.0', 'workers': 1, 'port': 8001, 'app': 'chatbot.entrypoint.api:app', 'uds': None, 'fd': None, 'reload': False...
           │         └ ()
           └ <function main at 0x799ab3868280>
  File "/usr/local/lib/python3.10/site-packages/uvicorn/main.py", line 412, in main
    run(
    └ <function run at 0x799ab3a2aef0>
  File "/usr/local/lib/python3.10/site-packages/uvicorn/main.py", line 579, in run
    server.run()
    │      └ <function Server.run at 0x799ab38c81f0>
    └ <uvicorn.server.Server object at 0x799ab3961420>
  File "/usr/local/lib/python3.10/site-packages/uvicorn/server.py", line 66, in run
    return asyncio.run(self.serve(sockets=sockets))
           │       │   │    │             └ None
           │       │   │    └ <function Server.serve at 0x799ab38c8280>
           │       │   └ <uvicorn.server.Server object at 0x799ab3961420>
           │       └ <function run at 0x799ab4489240>
           └ <module 'asyncio' from '/usr/local/lib/python3.10/asyncio/__init__.py'>
  File "/usr/local/lib/python3.10/asyncio/runners.py", line 44, in run
    return loop.run_until_complete(main)
           │    │                  └ <coroutine object Server.serve at 0x799ab389ece0>
           │    └ <function BaseEventLoop.run_until_complete at 0x799ab3b91990>
           └ <_UnixSelectorEventLoop running=True closed=False debug=False>
  File "/usr/local/lib/python3.10/asyncio/base_events.py", line 636, in run_until_complete
    self.run_forever()
    │    └ <function BaseEventLoop.run_forever at 0x799ab3b91900>
    └ <_UnixSelectorEventLoop running=True closed=False debug=False>
  File "/usr/local/lib/python3.10/asyncio/base_events.py", line 603, in run_forever
    self._run_once()
    │    └ <function BaseEventLoop._run_once at 0x799ab3b93400>
    └ <_UnixSelectorEventLoop running=True closed=False debug=False>
  File "/usr/local/lib/python3.10/asyncio/base_events.py", line 1909, in _run_once
    handle._run()
    │      └ <function Handle._run at 0x799ab3d0add0>
    └ <Handle <TaskStepMethWrapper object at 0x7999785f5600>()>
  File "/usr/local/lib/python3.10/asyncio/events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
    │    │            │    │           │    └ <member '_args' of 'Handle' objects>
    │    │            │    │           └ <Handle <TaskStepMethWrapper object at 0x7999785f5600>()>
    │    │            │    └ <member '_callback' of 'Handle' objects>
    │    │            └ <Handle <TaskStepMethWrapper object at 0x7999785f5600>()>
    │    └ <member '_context' of 'Handle' objects>
    └ <Handle <TaskStepMethWrapper object at 0x7999785f5600>()>
  File "/usr/local/lib/python3.10/site-packages/uvicorn/protocols/http/h11_impl.py", line 403, in run_asgi
    result = await app(  # type: ignore[func-returns-value]
                   └ <uvicorn.middleware.proxy_headers.ProxyHeadersMiddleware object at 0x799ab39c1f00>
INFO:     139.59.122.60:57456 - "POST /soulplay/describe_message HTTP/1.1" 500 Internal Server Error
  File "/usr/local/lib/python3.10/site-packages/uvicorn/middleware/proxy_headers.py", line 60, in __call__
    return await self.app(scope, receive, send)
                 │    │   │      │        └ <bound method RequestResponseCycle.send of <uvicorn.protocols.http.h11_impl.RequestResponseCycle object at 0x7999785f5330>>
                 │    │   │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.h11_impl.RequestResponseCycle object at 0x7999785f5330>>
                 │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('172.18.0.2', 8001), 'c...
                 │    └ <fastapi.applications.FastAPI object at 0x799a95906350>
                 └ <uvicorn.middleware.proxy_headers.ProxyHeadersMiddleware object at 0x799ab39c1f00>
  File "/usr/local/lib/python3.10/site-packages/fastapi/applications.py", line 1054, in __call__
    await super().__call__(scope, receive, send)
                           │      │        └ <bound method RequestResponseCycle.send of <uvicorn.protocols.http.h11_impl.RequestResponseCycle object at 0x7999785f5330>>
                           │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.h11_impl.RequestResponseCycle object at 0x7999785f5330>>
                           └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('172.18.0.2', 8001), 'c...
  File "/usr/local/lib/python3.10/site-packages/starlette/applications.py", line 112, in __call__
    await self.middleware_stack(scope, receive, send)
          │    │                │      │        └ <bound method RequestResponseCycle.send of <uvicorn.protocols.http.h11_impl.RequestResponseCycle object at 0x7999785f5330>>
          │    │                │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.h11_impl.RequestResponseCycle object at 0x7999785f5330>>
          │    │                └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('172.18.0.2', 8001), 'c...
          │    └ <starlette.middleware.errors.ServerErrorMiddleware object at 0x79997ea7ece0>
          └ <fastapi.applications.FastAPI object at 0x799a95906350>
  File "/usr/local/lib/python3.10/site-packages/starlette/middleware/errors.py", line 165, in __call__
    await self.app(scope, receive, _send)
          │    │   │      │        └ <function ServerErrorMiddleware.__call__.<locals>._send at 0x79997e075d80>
          │    │   │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.h11_impl.RequestResponseCycle object at 0x7999785f5330>>
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('172.18.0.2', 8001), 'c...
          │    └ <starlette.middleware.cors.CORSMiddleware object at 0x79997ea7ed10>
          └ <starlette.middleware.errors.ServerErrorMiddleware object at 0x79997ea7ece0>
  File "/usr/local/lib/python3.10/site-packages/starlette/middleware/cors.py", line 85, in __call__
    await self.app(scope, receive, send)
          │    │   │      │        └ <function ServerErrorMiddleware.__call__.<locals>._send at 0x79997e075d80>
          │    │   │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.h11_impl.RequestResponseCycle object at 0x7999785f5330>>
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('172.18.0.2', 8001), 'c...
          │    └ <starlette.middleware.exceptions.ExceptionMiddleware object at 0x79997ea7fcd0>
          └ <starlette.middleware.cors.CORSMiddleware object at 0x79997ea7ed10>
  File "/usr/local/lib/python3.10/site-packages/starlette/middleware/exceptions.py", line 62, in __call__
    await wrap_app_handling_exceptions(self.app, conn)(scope, receive, send)
          │                            │    │    │     │      │        └ <function ServerErrorMiddleware.__call__.<locals>._send at 0x79997e075d80>
          │                            │    │    │     │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.h11_impl.RequestResponseCycle object at 0x7999785f5330>>
          │                            │    │    │     └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('172.18.0.2', 8001), 'c...
          │                            │    │    └ <starlette.requests.Request object at 0x7999785f7fa0>
          │                            │    └ <fastapi.routing.APIRouter object at 0x79997e274d90>
          │                            └ <starlette.middleware.exceptions.ExceptionMiddleware object at 0x79997ea7fcd0>
          └ <function wrap_app_handling_exceptions at 0x799ab28cc790>
  File "/usr/local/lib/python3.10/site-packages/starlette/_exception_handler.py", line 42, in wrapped_app
    await app(scope, receive, sender)
          │   │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x79997e075c60>
          │   │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.h11_impl.RequestResponseCycle object at 0x7999785f5330>>
          │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('172.18.0.2', 8001), 'c...
          └ <fastapi.routing.APIRouter object at 0x79997e274d90>
  File "/usr/local/lib/python3.10/site-packages/starlette/routing.py", line 714, in __call__
    await self.middleware_stack(scope, receive, send)
          │    │                │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x79997e075c60>
          │    │                │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.h11_impl.RequestResponseCycle object at 0x7999785f5330>>
          │    │                └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('172.18.0.2', 8001), 'c...
          │    └ <bound method Router.app of <fastapi.routing.APIRouter object at 0x79997e274d90>>
          └ <fastapi.routing.APIRouter object at 0x79997e274d90>
  File "/usr/local/lib/python3.10/site-packages/starlette/routing.py", line 734, in app
    await route.handle(scope, receive, send)
          │     │      │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x79997e075c60>
          │     │      │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.h11_impl.RequestResponseCycle object at 0x7999785f5330>>
          │     │      └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('172.18.0.2', 8001), 'c...
          │     └ <function Route.handle at 0x799ab28ce050>
          └ APIRoute(path='/soulplay/describe_message', name='describe_message', methods=['POST'])
  File "/usr/local/lib/python3.10/site-packages/starlette/routing.py", line 288, in handle
    await self.app(scope, receive, send)
          │    │   │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x79997e075c60>
          │    │   │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.h11_impl.RequestResponseCycle object at 0x7999785f5330>>
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('172.18.0.2', 8001), 'c...
          │    └ <function request_response.<locals>.app at 0x79997e15c670>
          └ APIRoute(path='/soulplay/describe_message', name='describe_message', methods=['POST'])
  File "/usr/local/lib/python3.10/site-packages/starlette/routing.py", line 76, in app
    await wrap_app_handling_exceptions(app, request)(scope, receive, send)
          │                            │    │        │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x79997e075c60>
          │                            │    │        │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.h11_impl.RequestResponseCycle object at 0x7999785f5330>>
          │                            │    │        └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('172.18.0.2', 8001), 'c...
          │                            │    └ <starlette.requests.Request object at 0x7999785f70d0>
          │                            └ <function request_response.<locals>.app.<locals>.app at 0x79997e075cf0>
          └ <function wrap_app_handling_exceptions at 0x799ab28cc790>
  File "/usr/local/lib/python3.10/site-packages/starlette/_exception_handler.py", line 42, in wrapped_app
    await app(scope, receive, sender)
          │   │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x79997e075000>
          │   │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.h11_impl.RequestResponseCycle object at 0x7999785f5330>>
          │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('172.18.0.2', 8001), 'c...
          └ <function request_response.<locals>.app.<locals>.app at 0x79997e075cf0>
  File "/usr/local/lib/python3.10/site-packages/starlette/routing.py", line 73, in app
    response = await f(request)
                     │ └ <starlette.requests.Request object at 0x7999785f70d0>
                     └ <function get_request_handler.<locals>.app at 0x79997e15d480>
  File "/usr/local/lib/python3.10/site-packages/fastapi/routing.py", line 301, in app
    raw_response = await run_endpoint_function(
                         └ <function run_endpoint_function at 0x799ab28cfbe0>
  File "/usr/local/lib/python3.10/site-packages/fastapi/routing.py", line 212, in run_endpoint_function
    return await dependant.call(**values)
                 │         │      └ {'request': DescribeSchema(user_id='partnr-development-251', character_id='tomas', session_id='251_tomas_1754983830099_scenar...
                 │         └ <function describe_message at 0x799a8b0d75b0>
                 └ Dependant(path_params=[], query_params=[], header_params=[], cookie_params=[], body_params=[ModelField(field_info=Body(Pydant...

  File "/app/chatbot/utils/utils.py", line 157, in wrapper
    result = await func(*args, **kwargs)
                   │     │       └ {'request': DescribeSchema(user_id='partnr-development-251', character_id='tomas', session_id='251_tomas_1754983830099_scenar...
                   │     └ ()
                   └ <function describe_message at 0x799a8b0d7520>

> File "/app/chatbot/entrypoint/router/soulplay_button.py", line 111, in describe_message
    lang = last_assistant_message["message"]["content"].get("lang", "English")
           └ None

TypeError: 'NoneType' object is not subscriptable
2025-08-12 07:35:26.692 | INFO     | chatbot.utils.utils:wrapper:159 - Function describe_message took 0.09 seconds to execute